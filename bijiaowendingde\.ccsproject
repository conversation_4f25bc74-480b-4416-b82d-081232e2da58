<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="70.2.0"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/segger_j-link_connection.xml"/>
	<executableActions value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=empty_LP_MSPM0G3507_nortos_ticlang.projectspec.M0G3507_PID_N20,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="C:/Users/<USER>/Desktop/M0G3507_PID_310s"/>
	<filesToOpen value="README.md,empty.syscfg"/>
</projectOptions>
