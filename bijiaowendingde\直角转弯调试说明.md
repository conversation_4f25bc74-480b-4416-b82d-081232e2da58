# 直角转弯功能调试说明

## 问题分析
您原来的循迹代码存在以下问题，可能无法很好地处理直角转弯：

### 原代码问题：
1. **逻辑结构问题**：使用多个独立的`if`语句，没有优先级顺序
2. **直角转弯检测不完整**：只检测了部分传感器组合
3. **缺少else if结构**：可能导致多个条件同时满足，产生冲突
4. **转弯力度不够**：直角转弯时差速可能不够大

## 优化后的功能

### 1. 改进的循迹逻辑
现在使用`else if`结构，确保只有一个条件被执行：

```c
// 优化后的循迹逻辑
if(完全偏离黑线) {
    // 保持上次方向
}
else if(中间传感器检测到) {
    // 直行
}
else if(轻微左偏) {
    // 小角度左转
}
else if(严重左偏) {
    // 大角度左转
}
// ... 更多条件
```

### 2. 专门的直角转弯检测
新增了专门的直角转弯检测逻辑：

```c
// 左转直角
else if((D1==1 && D2==1 && D3==1 && D4==0 && D5==0) ||
        (D1==1 && D2==1 && D3==0 && D4==0 && D5==0))
{
    aim_speed1 = ZX_SPEED - RIGHT_GNGLE;  // 左轮慢
    aim_speed2 = ZX_SPEED + RIGHT_GNGLE;  // 右轮快
}

// 右转直角
else if((D1==0 && D2==0 && D3==1 && D4==1 && D5==1) ||
        (D1==0 && D2==0 && D3==0 && D4==1 && D5==1))
{
    aim_speed1 = ZX_SPEED + RIGHT_GNGLE;  // 左轮快
    aim_speed2 = ZX_SPEED - RIGHT_GNGLE;  // 右轮慢
}
```

### 3. 传感器组合说明
| 传感器状态 | 含义 | 动作 |
|------------|------|------|
| D1,D2,D3=1, D4,D5=0 | 左转直角 | 左轮慢，右轮快 |
| D1,D2=0, D3,D4,D5=1 | 右转直角 | 左轮快，右轮慢 |
| D1,D2,D3,D4,D5=1 | 十字路口 | 直行 |
| D1,D2=1, D3=0, D4,D5=0 | 左侧偏离 | 左转 |
| D1,D2=0, D3=0, D4,D5=1 | 右侧偏离 | 右转 |

## 调试建议

### 1. 速度参数调整
如果直角转弯效果不好，可以调整以下参数：

```c
#define ZX_SPEED        8    // 直行速度
#define MINI_GNGLE      3    // 小角度差速
#define BIG_GNGLE       5    // 大角度差速
#define RIGHT_GNGLE     5    // 直角转弯差速
```

**建议调整**：
- 如果转弯太慢：增加`RIGHT_GNGLE`的值
- 如果转弯太快：减少`RIGHT_GNGLE`的值
- 如果直行不稳定：调整`ZX_SPEED`

### 2. 传感器位置调整
确保传感器间距合适：
- 传感器间距：建议15-20mm
- 传感器高度：距离地面5-10mm
- 黑线宽度：建议20-30mm

### 3. 测试步骤
1. **基础测试**：先测试直行是否稳定
2. **小角度测试**：测试轻微偏离的修正
3. **大角度测试**：测试严重偏离的修正
4. **直角测试**：测试90度转弯
5. **连续转弯测试**：测试矩形路径

### 4. 常见问题及解决方案

#### 问题1：转弯时冲出黑线
**原因**：转弯力度不够或速度太快
**解决**：增加`RIGHT_GNGLE`值或降低`ZX_SPEED`

#### 问题2：转弯时抖动
**原因**：传感器检测不稳定
**解决**：检查传感器连接，调整传感器高度

#### 问题3：转弯角度不够
**原因**：差速太小
**解决**：增加`RIGHT_GNGLE`值

#### 问题4：转弯角度过大
**原因**：差速太大
**解决**：减少`RIGHT_GNGLE`值

## 测试代码
您可以在主循环中添加调试信息：

```c
// 在Veer_Trailing函数中添加调试输出
printf("D1=%d D2=%d D3=%d D4=%d D5=%d, Speed1=%d Speed2=%d\n", 
       D1, D2, D3, D4, D5, aim_speed1, aim_speed2);
```

这样可以观察传感器状态和电机速度的变化，帮助调试。 