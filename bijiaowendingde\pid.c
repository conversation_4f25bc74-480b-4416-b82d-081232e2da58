 /******************** (C) COPYRIGHT 2025   B站：电子大师兄 ****************************
* 文件名    : pid.c
* 作者      : 电子大师兄（https://space.bilibili.com/568487373）
* 版本      : V1.0
* 时间      : 2025-7-23
* 描述      : PID模块
*******************************************************************************/
#include "pid.h"
#include <stdlib.h>


PID_t MOTOR_A = {
         .param = { .kp = 100, .ki = 30, .kd = 10.0f, .outMax = 3200.0f, .outMin = -3200.0f },
        .integral = 0.0f,
        .lastError = 0.0f,
        .output = 0.0f
};
PID_t MOTOR_B = {
         .param = { .kp = 100, .ki = 30, .kd = 10.0f, .outMax = 3200.0f, .outMin = -3200.0f },
        .integral = 0.0f,
        .lastError = 0.0f,
        .output = 0.0f
};


/* 计算一次 PID
 * pid     : PID 控制器实例
 * setpoint: 期望值
 * feedback: 实际值
 * 返回值   : 本次 PID 输出
 */
float PID_Calc(PID_t *pid, float setpoint, float feedback)
{
    if (pid == NULL) return 0.0f;

    /* 1. 计算误差 */
    float error = setpoint - feedback;

    /* 2. 比例项 */
    float pOut = pid->param.kp * error;

    /* 3. 积分项（带抗积分饱和） */
    pid->integral += error;               // 先累加
    float integralTerm = pid->param.ki * pid->integral;

    /* 积分限幅：避免积分饱和 */
    float maxIntegral = pid->param.outMax / (pid->param.ki + 1e-6f);
    float minIntegral = pid->param.outMin / (pid->param.ki + 1e-6f);
    if (integralTerm > maxIntegral) {
        integralTerm = maxIntegral;
        pid->integral = maxIntegral / (pid->param.ki + 1e-6f);
    } else if (integralTerm < minIntegral) {
        integralTerm = minIntegral;
        pid->integral = minIntegral / (pid->param.ki + 1e-6f);
    }

    /* 4. 微分项（简单后向差分） */
    float derivative = error - pid->lastError;
    float dOut = pid->param.kd * derivative;
    pid->lastError = error;

    /* 5. 合并输出 */
    float output = pOut + integralTerm + dOut;

    /* 6. 输出限幅 */
    if (output > pid->param.outMax) output = pid->param.outMax;
    else if (output < pid->param.outMin) output = pid->param.outMin;

    pid->output = output;
    return output;
}