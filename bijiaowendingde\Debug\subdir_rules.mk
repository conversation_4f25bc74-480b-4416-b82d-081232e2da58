################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/diansai/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"D:/daima/xxxxxxxxx/M0G3507_PID_310s" -I"D:/daima/xxxxxxxxx/M0G3507_PID_310s/Debug" -I"D:/diansai/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/diansai/mspm0_sdk_2_05_01_00/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

build-701940097: ../main.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/diansai/sysconfig_1.24.0/sysconfig_cli.bat" --script "D:/daima/xxxxxxxxx/M0G3507_PID_310s/main.syscfg" -o "." -s "D:/diansai/mspm0_sdk_2_05_01_00/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-701940097 ../main.syscfg
device.opt: build-701940097
device.cmd.genlibs: build-701940097
ti_msp_dl_config.c: build-701940097
ti_msp_dl_config.h: build-701940097
Event.dot: build-701940097

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/diansai/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"D:/daima/xxxxxxxxx/M0G3507_PID_310s" -I"D:/daima/xxxxxxxxx/M0G3507_PID_310s/Debug" -I"D:/diansai/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/diansai/mspm0_sdk_2_05_01_00/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: D:/diansai/mspm0_sdk_2_05_01_00/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/diansai/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"D:/daima/xxxxxxxxx/M0G3507_PID_310s" -I"D:/daima/xxxxxxxxx/M0G3507_PID_310s/Debug" -I"D:/diansai/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/diansai/mspm0_sdk_2_05_01_00/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


