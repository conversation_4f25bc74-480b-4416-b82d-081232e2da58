# 循迹功能测试说明

## 修改内容总结

### 1. 速度参数调整
为了提高稳定性，降低了所有速度参数：
```c
#define ZX_SPEED        5    // 直行速度（从8降到5）
#define MINI_GNGLE      2    // 小角度差速（从3降到2）
#define BIG_GNGLE       3    // 大角度差速（从5降到3）
#define RIGHT_GNGLE     4    // 直角转弯差速（从5降到4）
```

### 2. 按键功能注释
- 注释掉了所有按键相关的代码
- 小车现在会自动开始循迹，运行1圈后停止

### 3. 专注于直角转弯
- 简化了循迹逻辑
- 重点优化了直角转弯的检测和控制

## 测试步骤

### 1. 硬件准备
- 确保循迹传感器连接正确（GPIOB.0-GPIOB.4）
- 确保电机连接正确
- 准备矩形黑线路径

### 2. 基础测试
1. **上电测试**：上电后小车应该自动开始循迹
2. **直行测试**：观察小车是否能沿直线稳定行驶
3. **小角度修正**：观察小车是否能修正轻微偏离

### 3. 直角转弯测试
重点测试以下情况：

#### 左转直角测试
- 传感器状态：`D1=1, D2=1, D3=1, D4=0, D5=0`
- 或：`D1=1, D2=1, D3=0, D4=0, D5=0`
- 预期动作：左轮慢，右轮快

#### 右转直角测试
- 传感器状态：`D1=0, D2=0, D3=1, D4=1, D5=1`
- 或：`D1=0, D2=0, D3=0, D4=1, D5=1`
- 预期动作：左轮快，右轮慢

### 4. 观察要点

#### 转弯效果观察
1. **转弯是否平滑**：观察转弯过程是否流畅
2. **是否冲出黑线**：观察转弯时是否偏离路径
3. **转弯角度是否合适**：观察是否能完成90度转弯

#### 速度观察
1. **直行速度**：观察直行时速度是否合适
2. **转弯速度**：观察转弯时速度是否合适
3. **整体稳定性**：观察整体运行是否稳定

## 调试建议

### 如果转弯效果不好：

#### 问题1：转弯太慢
**现象**：转弯时速度很慢，转弯角度不够
**解决**：增加`RIGHT_GNGLE`的值
```c
#define RIGHT_GNGLE     5    // 从4增加到5
```

#### 问题2：转弯太快
**现象**：转弯时速度太快，容易冲出黑线
**解决**：减少`RIGHT_GNGLE`的值
```c
#define RIGHT_GNGLE     3    // 从4减少到3
```

#### 问题3：直行不稳定
**现象**：直行时左右摇摆
**解决**：调整`ZX_SPEED`或`MINI_GNGLE`
```c
#define ZX_SPEED        4    // 从5减少到4
#define MINI_GNGLE      1    // 从2减少到1
```

#### 问题4：转弯时抖动
**现象**：转弯时电机抖动
**解决**：检查传感器连接，调整传感器高度

### 参数微调建议
根据测试结果，可以微调以下参数：

```c
// 如果整体太慢
#define ZX_SPEED        6    // 增加直行速度
#define RIGHT_GNGLE     5    // 增加转弯力度

// 如果整体太快
#define ZX_SPEED        4    // 减少直行速度
#define RIGHT_GNGLE     3    // 减少转弯力度

// 如果转弯不够
#define RIGHT_GNGLE     5    // 增加转弯力度

// 如果转弯过度
#define RIGHT_GNGLE     3    // 减少转弯力度
```

## 测试记录
建议记录以下信息：
- 测试时间
- 测试路径形状
- 转弯效果评价
- 遇到的问题
- 参数调整记录

这样可以逐步优化参数，找到最适合的设置。 