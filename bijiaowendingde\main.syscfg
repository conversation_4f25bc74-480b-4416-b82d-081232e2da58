/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4150"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

GPIO1.$name                          = "TB6612_IO";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "AIN1";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "12";
GPIO1.associatedPins[0].pin.$assign  = "PB12";
GPIO1.associatedPins[1].$name        = "AIN2";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "13";
GPIO1.associatedPins[1].pin.$assign  = "PB13";
GPIO1.associatedPins[2].$name        = "BIN1";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "15";
GPIO1.associatedPins[2].pin.$assign  = "PB15";
GPIO1.associatedPins[3].$name        = "BIN2";
GPIO1.associatedPins[3].assignedPort = "PORTB";
GPIO1.associatedPins[3].assignedPin  = "16";
GPIO1.associatedPins[3].pin.$assign  = "PB16";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "GPIO_Encoder";
GPIO2.associatedPins.create(4);
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].assignedPort     = "PORTB";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].$name            = "A1";
GPIO2.associatedPins[0].polarity         = "RISE";
GPIO2.associatedPins[0].assignedPin      = "6";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].assignedPort     = "PORTB";
GPIO2.associatedPins[1].interruptEn      = true;
GPIO2.associatedPins[1].$name            = "A2";
GPIO2.associatedPins[1].polarity         = "RISE";
GPIO2.associatedPins[1].assignedPin      = "7";
GPIO2.associatedPins[2].$name            = "B1";
GPIO2.associatedPins[2].direction        = "INPUT";
GPIO2.associatedPins[2].assignedPort     = "PORTB";
GPIO2.associatedPins[2].assignedPin      = "8";
GPIO2.associatedPins[2].interruptEn      = true;
GPIO2.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[2].polarity         = "RISE";
GPIO2.associatedPins[2].pin.$assign      = "PB8";
GPIO2.associatedPins[3].$name            = "B2";
GPIO2.associatedPins[3].direction        = "INPUT";
GPIO2.associatedPins[3].assignedPort     = "PORTB";
GPIO2.associatedPins[3].assignedPin      = "9";
GPIO2.associatedPins[3].interruptEn      = true;
GPIO2.associatedPins[3].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[3].polarity         = "RISE";
GPIO2.associatedPins[3].pin.$assign      = "PB9";

GPIO3.$name                              = "GPIO_HWCGQ";
GPIO3.associatedPins.create(7);
GPIO3.associatedPins[0].$name            = "PIN_0";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].internalResistor = "PULL_UP";
GPIO3.associatedPins[0].assignedPort     = "PORTB";
GPIO3.associatedPins[0].assignedPin      = "0";
GPIO3.associatedPins[1].$name            = "PIN_1";
GPIO3.associatedPins[1].internalResistor = "PULL_UP";
GPIO3.associatedPins[1].direction        = "INPUT";
GPIO3.associatedPins[1].assignedPort     = "PORTB";
GPIO3.associatedPins[1].assignedPin      = "1";
GPIO3.associatedPins[2].$name            = "PIN_2";
GPIO3.associatedPins[2].direction        = "INPUT";
GPIO3.associatedPins[2].internalResistor = "PULL_UP";
GPIO3.associatedPins[2].assignedPort     = "PORTB";
GPIO3.associatedPins[2].assignedPin      = "2";
GPIO3.associatedPins[3].$name            = "PIN_3";
GPIO3.associatedPins[3].direction        = "INPUT";
GPIO3.associatedPins[3].internalResistor = "PULL_UP";
GPIO3.associatedPins[3].assignedPort     = "PORTB";
GPIO3.associatedPins[3].assignedPin      = "3";
GPIO3.associatedPins[4].$name            = "PIN_4";
GPIO3.associatedPins[4].direction        = "INPUT";
GPIO3.associatedPins[4].internalResistor = "PULL_UP";
GPIO3.associatedPins[4].assignedPort     = "PORTB";
GPIO3.associatedPins[4].assignedPin      = "4";
GPIO3.associatedPins[5].$name            = "PIN_5";
GPIO3.associatedPins[5].direction        = "INPUT";
GPIO3.associatedPins[5].assignedPort     = "PORTB";
GPIO3.associatedPins[5].assignedPin      = "5";
GPIO3.associatedPins[6].$name            = "PIN_6";
GPIO3.associatedPins[6].direction        = "INPUT";
GPIO3.associatedPins[6].assignedPort     = "PORTB";
GPIO3.associatedPins[6].assignedPin      = "10";

PWM1.timerCount                         = 3200;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.$name                              = "TB6612_PWM";
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PA13";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 10;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle            = 10;
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable    = true;
SYSTICK.period          = 320000;
SYSTICK.interruptEnable = true;
SYSTICK.systickEnable   = true;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkPrescale   = 32;
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerPeriod        = "10ms";
TIMER1.peripheral.$assign = "TIMA1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB6";
GPIO2.associatedPins[1].pin.$suggestSolution = "PB7";
GPIO3.associatedPins[0].pin.$suggestSolution = "PB0";
GPIO3.associatedPins[1].pin.$suggestSolution = "PB1";
GPIO3.associatedPins[2].pin.$suggestSolution = "PB2";
GPIO3.associatedPins[3].pin.$suggestSolution = "PB3";
GPIO3.associatedPins[4].pin.$suggestSolution = "PB4";
GPIO3.associatedPins[5].pin.$suggestSolution = "PB5";
GPIO3.associatedPins[6].pin.$suggestSolution = "PB10";
