 /******************** (C) COPYRIGHT 2025   B站：电子大师兄 ****************************
* 文件名    : encoder.c
* 作者      : 电子大师兄（https://space.bilibili.com/568487373）
* 版本      : V1.0
* 时间      : 2025-7-23
* 描述      : 编码器模块
*******************************************************************************/
#include "encoder.h"

int32_t encoder_cnt1 = 0;
int32_t encoder_cnt2 = 0;

int32_t cur_speed1 = 0;    //单位：脉冲/10毫秒
int32_t cur_speed2 = 0;    //单位：脉冲/10毫秒

/*******************************************************************************
* 函数名 : init_encoder
* 描述    : 初始化编码器模块
* 作者    : 电子大师兄
* 输入    : None
* 输出    : None
* 返回    : None
*******************************************************************************/
void init_encoder(void)
{
    NVIC_ClearPendingIRQ(GPIO_Encoder_INT_IRQN);
    NVIC_EnableIRQ(GPIO_Encoder_INT_IRQN);
}

/*******************************************************************************
* 函数名 : calc_speed
* 描述    : 计算当前速度值，需在定时器中断或Systick定时器中断中，每10ms调用一次
* 作者    : 电子大师兄
* 输入    : None
* 输出    : None
* 返回    : None
*******************************************************************************/
void calc_speed(void)    //需在定时器中断或Systick定时器中断中，每10ms调用一次
{
    cur_speed1 = encoder_cnt1;  
    encoder_cnt1 = 0;

    cur_speed2 = encoder_cnt2;  
    encoder_cnt2 = 0;
}

/*******************************************************************************
* 函数名 : calc_encoder_cnt
* 描述    : 计算正转或者反转的脉冲数，要在编码器接口中断或者编码器外部中断中调用
* 作者    : 电子大师兄
* 输入    : None
* 输出    : None
* 返回    : None
*******************************************************************************/
void calc_encoder_cnt(void)  //在编码器接口中断或者编码器外部中断中调用，计算正转或者反转的脉冲数
{
   
   
    //获取中断信号
    uint32_t gpioB = DL_GPIO_getEnabledInterruptStatus(GPIOB, GPIO_Encoder_A1_PIN | GPIO_Encoder_A2_PIN | GPIO_Encoder_B1_PIN | GPIO_Encoder_B2_PIN);
 
    //如果是GPIO_EncoderA_PIN_0_PIN产生的中断
    if((gpioB & GPIO_Encoder_A1_PIN) == GPIO_Encoder_A1_PIN)
    {
        //Pin0上升沿，看Pin1的电平，为低电平则判断为反转，高电平判断为正转
        if(DL_GPIO_readPins(GPIOB,GPIO_Encoder_A2_PIN))//P1为高电平
        {
            encoder_cnt1--;
        }
        else//P1为高电平
        {
            encoder_cnt1++;
        }
    }
    
    //类似于Stm32中编码器模式的AB两相都测，可得到2倍的计数
    else if((gpioB & GPIO_Encoder_A2_PIN) == GPIO_Encoder_A2_PIN)
    {
        //Pin1上升沿
        if(DL_GPIO_readPins(GPIOB,GPIO_Encoder_A1_PIN))//P0为高电平
        {
             encoder_cnt1++;
        }
        else//P1为高电平
        {
              encoder_cnt1--;
        }
    }
    
   //如果是GPIO_EncoderA_PIN_0_PIN产生的中断
    if((gpioB & GPIO_Encoder_B1_PIN) == GPIO_Encoder_B1_PIN)
    {
        //Pin0上升沿，看Pin1的电平，为低电平则判断为反转，高电平判断为正转
        if(DL_GPIO_readPins(GPIOB,GPIO_Encoder_B2_PIN))//P1为高电平
        {
            encoder_cnt2++;
            
        }
        else//P1为高电平
        {
            encoder_cnt2--;
            
        }
    }
    
    //类似于Stm32中编码器模式的AB两相都测，可得到2倍的计数
    else if((gpioB & GPIO_Encoder_B2_PIN) == GPIO_Encoder_B2_PIN)
    {
        //Pin1上升沿
        if(DL_GPIO_readPins(GPIOB,GPIO_Encoder_B1_PIN))//P0为高电平
        {
            encoder_cnt2--;
       
        }
        else//P1为高电平
        {
            encoder_cnt2++;
            
        }
    }

    //最后清除中断标志位
    DL_GPIO_clearInterruptStatus(GPIOB, GPIO_Encoder_A1_PIN | GPIO_Encoder_A2_PIN | GPIO_Encoder_B1_PIN | GPIO_Encoder_B2_PIN);

    
}



