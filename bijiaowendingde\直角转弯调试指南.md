# 直角转弯调试指南

## 问题描述
循迹功能正常，但遇到直角转弯时不会转。

## 解决方案

### 1. 已实施的优化

#### 参数调整
```c
#define RIGHT_GNGLE     20   // 增大直角转弯差速（从15增加到20）
```

#### 算法优化
- **单传感器直角转弯**：最外侧传感器检测到时，内侧轮停止，外侧轮全速
- **复合传感器检测**：多个同侧传感器检测到时，采用更激进的转弯策略
- **动态差速计算**：根据检测到的传感器数量动态调整差速

### 2. 测试步骤

#### 步骤1：测试直角转弯功能
1. 设置 `test_mode = 3`（直角转弯测试模式）
2. 编译并下载程序
3. 将小车放在直角转弯处
4. 观察转弯效果

#### 步骤2：测试完整循迹
1. 设置 `test_mode = 0`（正常循迹模式）
2. 测试直角转弯效果
3. 根据效果调整参数

### 3. 直角转弯算法说明

#### 单传感器直角转弯
```c
// 左直角转弯
if(D1 == 1)  // 最左侧传感器
{
    aim_speed1 = 0;        // 左轮停止
    aim_speed2 = 30;       // 右轮全速
}

// 右直角转弯
if(D7 == 1)  // 最右侧传感器
{
    aim_speed1 = 30;       // 左轮全速
    aim_speed2 = 0;        // 右轮停止
}
```

#### 复合传感器直角转弯
```c
// 多个左侧传感器检测到
if(left_count >= 2)
{
    aim_speed1 = ZX_SPEED - (left_count * 6);  // 左轮减速
    aim_speed2 = ZX_SPEED + (left_count * 6);  // 右轮加速
}
```

### 4. 参数调整建议

#### 如果直角转弯太慢
```c
#define RIGHT_GNGLE     25   // 增大直角转弯差速
```

#### 如果直角转弯太急
```c
#define RIGHT_GNGLE     15   // 减小直角转弯差速
```

#### 如果转弯半径太大
- 增大复合传感器的差速倍数（从6增加到8）
- 减小基础速度 `ZX_SPEED`

#### 如果转弯半径太小
- 减小复合传感器的差速倍数（从6减小到4）
- 增大基础速度 `ZX_SPEED`

### 5. 常见问题排查

#### 问题1：直角转弯时小车不转
**可能原因**：
- 传感器检测不准确
- 差速参数太小
- 电机功率不够

**解决方法**：
1. 检查传感器是否正常工作
2. 增大 `RIGHT_GNGLE` 参数
3. 检查电池电量和电机状态

#### 问题2：直角转弯时小车转过了
**可能原因**：
- 差速参数太大
- 转弯检测逻辑过于敏感

**解决方法**：
1. 减小 `RIGHT_GNGLE` 参数
2. 调整复合传感器检测的差速倍数

#### 问题3：直角转弯时小车卡住
**可能原因**：
- 传感器检测不稳定
- 算法逻辑问题

**解决方法**：
1. 清洁传感器表面
2. 调整传感器安装高度
3. 检查黑线质量

### 6. 调试技巧

#### 观察传感器状态
在直角转弯测试模式下，观察以下情况：
- 最外侧传感器（D1或D7）是否正常检测
- 多个传感器是否同时检测到
- 传感器检测是否稳定

#### 观察速度设置
观察 `aim_speed1` 和 `aim_speed2` 的值：
- 直角转弯时是否出现一个轮子停止，另一个轮子加速
- 差速是否足够大

#### 逐步调整
1. 先测试直角转弯测试模式（test_mode = 3）
2. 确认基本转弯功能正常
3. 再测试完整循迹模式（test_mode = 0）
4. 根据效果微调参数

### 7. 测试环境建议

#### 直角转弯路径
- 使用标准的90度直角转弯
- 确保黑线清晰，宽度2-3cm
- 转弯半径建议5-10cm

#### 传感器安装
- 传感器距离地面5-10mm
- 确保传感器排列对称
- 定期清洁传感器表面

### 8. 预期效果

#### 理想效果
- 遇到直角转弯时，小车能够快速转向
- 转弯过程中保持稳定，不卡顿
- 转弯完成后能够继续循迹

#### 调整目标
- 转弯时间：1-2秒完成90度转弯
- 转弯稳定性：转弯过程中不偏离黑线
- 转弯精度：转弯后能够准确回到循迹状态

## 技术支持

如果直角转弯仍有问题，请提供：
1. 具体的转弯现象描述
2. 传感器检测状态（D1-D7的值）
3. 转弯时的速度设置（aim_speed1, aim_speed2）
4. 测试环境条件 