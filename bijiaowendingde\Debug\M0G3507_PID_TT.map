******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 24 15:53:02 2025

OUTPUT FILE NAME:   <M0G3507_PID_TT.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000008ad


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000a10  0001f5f0  R  X
  SRAM                  20200000   00008000  00000231  00007dcf  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000a10   00000a10    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000918   00000918    r-x .text
  000009d8    000009d8    00000010   00000010    r-- .rodata
  000009e8    000009e8    00000028   00000028    r-- .cinit
20200000    20200000    00000031   00000000    rw-
  20200000    20200000    00000031   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000918     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    000000fc     pid.o (.text.Pid_Speed)
                  000002c0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000398    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000432    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000434    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000004c0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000053c    00000078     main.o (.text.main)
                  000005b4    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWMA_init)
                  00000620    00000060     encoder.o (.text.calc_encoder_cnt)
                  00000680    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000006dc    00000050     tb6612_moto.o (.text.n20_moto)
                  0000072c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00000768    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000007a4    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000007de    00000002     --HOLE-- [fill = 0]
                  000007e0    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00000818    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000084c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000087c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000008ac    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000008d4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000008f0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000090c    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000924    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000093c    00000018     encoder.o (.text.calc_speed)
                  00000954    00000014     encoder.o (.text.init_encoder)
                  00000968    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000097a    00000002     --HOLE-- [fill = 0]
                  0000097c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000098c    00000010     tb6612_moto.o (.text.init_moto)
                  0000099c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000009a6    00000008     main.o (.text.GROUP1_IRQHandler)
                  000009ae    00000008     main.o (.text.SysTick_Handler)
                  000009b6    00000002     --HOLE-- [fill = 0]
                  000009b8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000009c0    00000006     libc.a : exit.c.obj (.text:abort)
                  000009c6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000009ca    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000009ce    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000009d2    00000006     --HOLE-- [fill = 0]

.cinit     0    000009e8    00000028     
                  000009e8    00000015     (.cinit..data.load) [load image, compression = lzss]
                  000009fd    00000003     --HOLE-- [fill = 0]
                  00000a00    00000008     (__TI_handler_table)
                  00000a08    00000008     (__TI_cinit_table)

.rodata    0    000009d8    00000010     
                  000009d8    00000008     ti_msp_dl_config.o (.rodata.gPWMAConfig)
                  000009e0    00000003     ti_msp_dl_config.o (.rodata.gPWMAClockConfig)
                  000009e3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000031     UNINITIALIZED
                  20200000    00000004     pid.o (.data.Pid_Speed.cal_pwm)
                  20200004    00000004     pid.o (.data.Pid_Speed.last_err.0)
                  20200008    00000004     pid.o (.data.Pid_Speed.last_err.1)
                  2020000c    00000004     pid.o (.data.Pid_Speed.last_speed)
                  20200010    00000004     pid.o (.data.Pid_Speed.remainder)
                  20200014    00000004     main.o (.data.aim_speed)
                  20200018    00000004     encoder.o (.data.cur_speed)
                  2020001c    00000004     encoder.o (.data.encoder_cnt)
                  20200020    00000004     tb6612_moto.o (.data.g_pwm)
                  20200024    00000004     pid.o (.data.kd)
                  20200028    00000004     pid.o (.data.ki)
                  2020002c    00000004     pid.o (.data.kp)
                  20200030    00000001     tb6612_moto.o (.data.g_dir)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             372    11        0      
       pid.o                          252    0         32     
       startup_mspm0g350x_ticlang.o   6      192       0      
       encoder.o                      140    0         8      
       main.o                         136    0         4      
       tb6612_moto.o                  96     0         5      
    +--+------------------------------+------+---------+---------+
       Total:                         1002   203       49     
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         406    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         538    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      37        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2316   240       561    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000a08 records: 1, size/record: 8, table size: 8
	.data: load addr=000009e8, load size=00000015 bytes, run addr=20200000, run size=00000031 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000a00 records: 2, size/record: 4, table size: 8
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000433  ADC0_IRQHandler                 
00000433  ADC1_IRQHandler                 
00000433  AES_IRQHandler                  
000009c6  C$$EXIT                         
00000433  CANFD0_IRQHandler               
00000433  DAC0_IRQHandler                 
0000099d  DL_Common_delayCycles           
000000c1  DL_Timer_initFourCCPWMMode      
000008d5  DL_Timer_setCaptCompUpdateMethod
0000090d  DL_Timer_setCaptureCompareOutCtl
0000097d  DL_Timer_setCaptureCompareValue 
000008f1  DL_Timer_setClockConfig         
00000433  DMA_IRQHandler                  
00000433  Default_Handler                 
00000433  GROUP0_IRQHandler               
000009a7  GROUP1_IRQHandler               
000009c7  HOSTexit                        
00000433  HardFault_Handler               
00000433  I2C0_IRQHandler                 
00000433  I2C1_IRQHandler                 
00000433  NMI_Handler                     
00000433  PendSV_Handler                  
000001c5  Pid_Speed                       
00000433  RTC_IRQHandler                  
000009cb  Reset_Handler                   
00000433  SPI0_IRQHandler                 
00000433  SPI1_IRQHandler                 
00000433  SVC_Handler                     
00000681  SYSCFG_DL_GPIO_init             
000005b5  SYSCFG_DL_PWMA_init             
0000084d  SYSCFG_DL_SYSCTL_init           
0000087d  SYSCFG_DL_SYSTICK_init          
00000925  SYSCFG_DL_init                  
00000819  SYSCFG_DL_initPower             
000009af  SysTick_Handler                 
00000433  TIMA0_IRQHandler                
00000433  TIMA1_IRQHandler                
00000433  TIMG0_IRQHandler                
00000433  TIMG12_IRQHandler               
00000433  TIMG6_IRQHandler                
00000433  TIMG7_IRQHandler                
00000433  TIMG8_IRQHandler                
00000433  UART0_IRQHandler                
00000433  UART1_IRQHandler                
00000433  UART2_IRQHandler                
00000433  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000a08  __TI_CINIT_Base                 
00000a10  __TI_CINIT_Limit                
00000a10  __TI_CINIT_Warm                 
00000a00  __TI_Handler_Table_Base         
00000a08  __TI_Handler_Table_Limit        
00000769  __TI_auto_init_nobinit_nopinit  
000004c1  __TI_decompress_lzss            
00000969  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000002cb  __addsf3                        
000007e1  __aeabi_f2iz                    
000002cb  __aeabi_fadd                    
00000435  __aeabi_fmul                    
000002c1  __aeabi_fsub                    
0000072d  __aeabi_i2f                     
000009b9  __aeabi_memcpy                  
000009b9  __aeabi_memcpy4                 
000009b9  __aeabi_memcpy8                 
ffffffff  __binit__                       
000007e1  __fixsfsi                       
0000072d  __floatsisf                     
UNDEFED   __mpu_init                      
000007a5  __muldsi3                       
00000435  __mulsf3                        
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000002c1  __subsf3                        
000008ad  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000009cf  _system_pre_init                
000009c1  abort                           
20200014  aim_speed                       
ffffffff  binit                           
00000621  calc_encoder_cnt                
0000093d  calc_speed                      
20200018  cur_speed                       
2020001c  encoder_cnt                     
20200030  g_dir                           
20200020  g_pwm                           
00000955  init_encoder                    
0000098d  init_moto                       
00000000  interruptVectors                
20200024  kd                              
20200028  ki                              
2020002c  kp                              
0000053d  main                            
00000399  memcpy                          
000006dd  n20_moto                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  DL_Timer_initFourCCPWMMode      
000001c5  Pid_Speed                       
00000200  __STACK_SIZE                    
000002c1  __aeabi_fsub                    
000002c1  __subsf3                        
000002cb  __addsf3                        
000002cb  __aeabi_fadd                    
00000399  memcpy                          
00000433  ADC0_IRQHandler                 
00000433  ADC1_IRQHandler                 
00000433  AES_IRQHandler                  
00000433  CANFD0_IRQHandler               
00000433  DAC0_IRQHandler                 
00000433  DMA_IRQHandler                  
00000433  Default_Handler                 
00000433  GROUP0_IRQHandler               
00000433  HardFault_Handler               
00000433  I2C0_IRQHandler                 
00000433  I2C1_IRQHandler                 
00000433  NMI_Handler                     
00000433  PendSV_Handler                  
00000433  RTC_IRQHandler                  
00000433  SPI0_IRQHandler                 
00000433  SPI1_IRQHandler                 
00000433  SVC_Handler                     
00000433  TIMA0_IRQHandler                
00000433  TIMA1_IRQHandler                
00000433  TIMG0_IRQHandler                
00000433  TIMG12_IRQHandler               
00000433  TIMG6_IRQHandler                
00000433  TIMG7_IRQHandler                
00000433  TIMG8_IRQHandler                
00000433  UART0_IRQHandler                
00000433  UART1_IRQHandler                
00000433  UART2_IRQHandler                
00000433  UART3_IRQHandler                
00000435  __aeabi_fmul                    
00000435  __mulsf3                        
000004c1  __TI_decompress_lzss            
0000053d  main                            
000005b5  SYSCFG_DL_PWMA_init             
00000621  calc_encoder_cnt                
00000681  SYSCFG_DL_GPIO_init             
000006dd  n20_moto                        
0000072d  __aeabi_i2f                     
0000072d  __floatsisf                     
00000769  __TI_auto_init_nobinit_nopinit  
000007a5  __muldsi3                       
000007e1  __aeabi_f2iz                    
000007e1  __fixsfsi                       
00000819  SYSCFG_DL_initPower             
0000084d  SYSCFG_DL_SYSCTL_init           
0000087d  SYSCFG_DL_SYSTICK_init          
000008ad  _c_int00_noargs                 
000008d5  DL_Timer_setCaptCompUpdateMethod
000008f1  DL_Timer_setClockConfig         
0000090d  DL_Timer_setCaptureCompareOutCtl
00000925  SYSCFG_DL_init                  
0000093d  calc_speed                      
00000955  init_encoder                    
00000969  __TI_decompress_none            
0000097d  DL_Timer_setCaptureCompareValue 
0000098d  init_moto                       
0000099d  DL_Common_delayCycles           
000009a7  GROUP1_IRQHandler               
000009af  SysTick_Handler                 
000009b9  __aeabi_memcpy                  
000009b9  __aeabi_memcpy4                 
000009b9  __aeabi_memcpy8                 
000009c1  abort                           
000009c6  C$$EXIT                         
000009c7  HOSTexit                        
000009cb  Reset_Handler                   
000009cf  _system_pre_init                
00000a00  __TI_Handler_Table_Base         
00000a08  __TI_CINIT_Base                 
00000a08  __TI_Handler_Table_Limit        
00000a10  __TI_CINIT_Limit                
00000a10  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200014  aim_speed                       
20200018  cur_speed                       
2020001c  encoder_cnt                     
20200020  g_pwm                           
20200024  kd                              
20200028  ki                              
2020002c  kp                              
20200030  g_dir                           
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[111 symbols]
