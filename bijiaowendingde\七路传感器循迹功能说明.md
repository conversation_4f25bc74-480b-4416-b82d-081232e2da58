# 七路传感器循迹功能说明

## 功能概述

本项目实现了基于七路红外传感器的基本循迹功能，支持：
- 精确的路径跟踪
- 差速转向控制

## 硬件配置

### 传感器配置
- **传感器数量**: 7个红外传感器
- **引脚分配**: 
  - D1: PB0 (最左侧传感器)
  - D2: PB1 (左外侧传感器)
  - D3: PB2 (左内侧传感器)
  - D4: PB3 (中心传感器)
  - D5: PB4 (右内侧传感器)
  - D6: PB5 (右外侧传感器)
  - D7: PB10 (最右侧传感器)

### 电机配置
- **左轮**: 通过TB6612驱动
- **右轮**: 通过TB6612驱动
- **PWM频率**: 可调节
- **编码器**: 用于速度反馈

## 软件功能

### 巡线算法

#### 基本巡线逻辑
```c
// 完美居中 - 只有中间传感器检测到黑线
if(D1==0 && D2==0 && D3==0 && D4==1 && D5==0 && D6==0 && D7==0)
{
    aim_speed1 = ZX_SPEED;  // 直行
    aim_speed2 = ZX_SPEED;
}
```

#### 偏离修正
- **轻微偏离**: 使用小角度差速修正
- **中等偏离**: 使用大角度差速修正
- **严重偏离**: 使用直角转弯差速修正

## 参数调整

### 速度参数
```c
#define ZX_SPEED        5    // 直行速度
#define MINI_GNGLE      2    // 小角度修正差速
#define BIG_GNGLE       4    // 大角度修正差速
#define RIGHT_GNGLE     8    // 直角转弯差速
```

### 调整建议
1. **ZX_SPEED**: 根据场地大小调整，建议3-8
2. **MINI_GNGLE**: 轻微偏离修正，建议1-3
3. **BIG_GNGLE**: 中等偏离修正，建议3-6
4. **RIGHT_GNGLE**: 直角转弯，建议6-12

## 使用步骤

### 1. 硬件准备
1. 确保七路传感器正确连接
2. 检查电机和编码器连接
3. 准备循迹场地（黑线路径）

### 2. 软件配置
1. 编译并下载程序
2. 调整速度参数

### 3. 测试
1. 将小车放在黑线路径上
2. 小车会自动开始循迹

### 4. 参数优化
1. 根据循迹效果调整速度参数
2. 优化差速参数提高转弯精度

## 常见问题

### 1. 传感器无响应
- 检查传感器连接
- 验证电源电压

### 2. 循迹不准确
- 调整差速参数
- 检查传感器安装高度
- 优化循迹算法

## 技术支持

如有问题，请参考：
- 传感器问题诊断指南.md
- 参数调整说明.md 