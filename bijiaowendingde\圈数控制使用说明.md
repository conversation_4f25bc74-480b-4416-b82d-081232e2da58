# 小车矩形循迹圈数控制功能说明

## 功能概述
本功能允许小车沿着矩形黑线循迹，并通过单个按键控制小车运行的圈数。小车会自动检测起点，完成指定圈数后自动停止。

## 硬件连接
- 循迹传感器连接到GPIOB.0-GPIOB.4（D1-D5）
- 控制按键连接到GPIOB.0（与D1共用）
- 电机驱动使用TB6612模块

## 单按键控制说明
使用GPIOB.0作为控制按键，通过不同的操作方式设置圈数：

| 操作方式 | 功能说明 |
|----------|----------|
| 短按1次 | 运行1圈 |
| 短按2次 | 运行2圈 |
| 短按3次 | 运行3圈 |
| 短按4次 | 运行4圈 |
| 短按5次 | 运行5圈 |
| 短按6次 | 重新开始，运行1圈 |
| 长按 | 立即停止当前运动 |

## 操作时序
1. **短按**：按下按键后快速释放（按下时间 < 500ms）
2. **长按**：按下按键并保持（按下时间 ≥ 500ms）
3. **连续短按**：可以连续短按来增加圈数，系统会记住按键次数

## 工作原理
1. **起点检测**：当所有5个循迹传感器都检测到黑线时，系统认为小车回到起点
2. **圈数计数**：每次检测到起点时，圈数计数器加1
3. **自动停止**：当达到目标圈数时，小车自动停止
4. **按键记忆**：系统会记住按键按下的次数，下次短按时会设置对应的圈数

## 使用方法
1. 将小车放置在矩形黑线的起点位置
2. 短按按键1-5次选择要运行的圈数
3. 小车开始沿黑线循迹
4. 完成指定圈数后，小车自动停止
5. 如需紧急停止，长按按键即可

## 技术特点
- **单按键控制**：只需一个按键即可控制所有功能
- **智能起点检测**：通过所有传感器同时检测黑线来判断起点
- **防误触发**：使用时间间隔防止重复检测
- **实时控制**：可以随时通过按键重新设置圈数或停止运动
- **自动停止**：达到目标圈数后自动停止，无需人工干预
- **按键记忆**：系统会记住按键次数，方便连续操作

## 注意事项
1. 确保矩形黑线完整，没有断点
2. 起点位置应该能让所有传感器都检测到黑线
3. 小车速度适中，确保循迹精度
4. 按键按下时确保接触良好
5. 短按和长按的时间阈值可以根据需要调整

## 代码结构
- `Trailing.h`：循迹和圈数控制头文件
- `Trailing.c`：循迹和圈数控制实现
- `main.c`：主程序，调用圈数控制功能

## 调试建议
1. 如果圈数计数不准确，可以调整起点检测的传感器组合
2. 如果循迹不稳定，可以调整速度参数
3. 如果按键响应不灵敏，可以调整短按和长按的时间阈值
4. 如果按键记忆功能异常，检查按键状态检测逻辑 