<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.0.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\bin\tiarmlnk -ID:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib -o M0G3507_PID_310s.out -mM0G3507_PID_310s.map -iD:/diansai/mspm0_sdk_2_05_01_00/source -iD:/daima/xxxxxxxxx/M0G3507_PID_310s -iD:/daima/xxxxxxxxx/M0G3507_PID_310s/Debug/syscfg -iD:/diansai/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=M0G3507_PID_310s_linkInfo.xml --rom_model ./Trailing.o ./encoder.o ./main.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./pid.o ./tb6612_moto.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688d2148</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\M0G3507_PID_310s.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1705</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>Trailing.o</file>
         <name>Trailing.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\.\</path>
         <kind>object</kind>
         <file>tb6612_moto.o</file>
         <name>tb6612_moto.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>D:\daima\xxxxxxxxx\M0G3507_PID_310s\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\diansai\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\diansai\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\diansai\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-cc">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-cd">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-ce">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-cf">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-d0">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-d1">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-d2">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.Veer_Trailing</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x5b4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text.PID_Calc</name>
         <load_address>0x674</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x674</run_address>
         <size>0x154</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x7c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c8</run_address>
         <size>0x130</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-145">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x8f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8f8</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.Trailing_detection</name>
         <load_address>0x9fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9fc</run_address>
         <size>0xf8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text.calc_encoder_cnt</name>
         <load_address>0xaf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf4</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xbe8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbe8</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0xcd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcd0</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text</name>
         <load_address>0xdac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdac</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.Motor_SetSPeed</name>
         <load_address>0xe84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe84</run_address>
         <size>0xb8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.text.TIMA1_IRQHandler</name>
         <load_address>0xf3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf3c</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.SYSCFG_DL_TB6612_PWM_init</name>
         <load_address>0xfd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfd4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.__mulsf3</name>
         <load_address>0x1064</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1064</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-cd"/>
      </object_component>
      <object_component id="oc-162">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x10f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f0</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.__divsf3</name>
         <load_address>0x1174</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1174</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x11f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11f6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x11f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11f8</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x1274</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1274</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x12d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12d8</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.SysTick_Config</name>
         <load_address>0x1330</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1330</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x1380</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1380</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x13c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13c4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1400</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1400</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-66">
         <name>.text.__floatsisf</name>
         <load_address>0x143c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x143c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.__gtsf2</name>
         <load_address>0x1478</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1478</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x14b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14b4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.__eqsf2</name>
         <load_address>0x14f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14f0</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.__muldsi3</name>
         <load_address>0x152c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x152c</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.__fixsfsi</name>
         <load_address>0x1568</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1568</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x15a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15a0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x15d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15d4</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1608</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1608</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1634</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1634</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1660</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1660</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x168c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x168c</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x16b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16b4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.calc_speed</name>
         <load_address>0x16dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16dc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-56">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1704</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1704</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.main</name>
         <load_address>0x172c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x172c</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.DL_SYSCTL_setFlashWaitState</name>
         <load_address>0x1750</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1750</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-98">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1770</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1770</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x178c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x178c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x17a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17a8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x17c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_GPIO_setLowerPinsPolarity</name>
         <load_address>0x17e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17e0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-138">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x17fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17fc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x1818</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1818</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x1834</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1834</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x1850</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1850</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-144">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x186c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x186c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1888</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1888</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x18a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18a0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x18b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-130">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x18d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18d0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x18e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18e8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1900</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1900</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-136">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1918</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1918</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1930</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1930</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1948</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1948</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1960</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1960</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x1978</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1978</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1990</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1990</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x19a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19a6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.init_encoder</name>
         <load_address>0x19bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19bc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x19d2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19d2</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x19e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19e8</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x19fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19fc</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x1a10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a10</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1a24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a24</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1a38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a38</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-64">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x1a4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a4c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x1a5e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a5e</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x1a70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a70</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-139">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x1a84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a84</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x1a94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a94</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x1aa4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1aa4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.init_moto</name>
         <load_address>0x1ab4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ab4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1ac4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ac4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x1ace</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ace</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1ad8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1ae0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ae0</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1ae4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ae4</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-87">
         <name>.text._system_pre_init</name>
         <load_address>0x1ae8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ae8</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text:abort</name>
         <load_address>0x1aec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1aec</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.cinit..data.load</name>
         <load_address>0x1b40</load_address>
         <readonly>true</readonly>
         <run_address>0x1b40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a2">
         <name>__TI_handler_table</name>
         <load_address>0x1b60</load_address>
         <readonly>true</readonly>
         <run_address>0x1b60</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a5">
         <name>.cinit..bss.load</name>
         <load_address>0x1b6c</load_address>
         <readonly>true</readonly>
         <run_address>0x1b6c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a3">
         <name>__TI_cinit_table</name>
         <load_address>0x1b74</load_address>
         <readonly>true</readonly>
         <run_address>0x1b74</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-143">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x1af0</load_address>
         <readonly>true</readonly>
         <run_address>0x1af0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-150">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x1b18</load_address>
         <readonly>true</readonly>
         <run_address>0x1b18</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.rodata.gTB6612_PWMConfig</name>
         <load_address>0x1b2c</load_address>
         <readonly>true</readonly>
         <run_address>0x1b2c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.rodata.gTB6612_PWMClockConfig</name>
         <load_address>0x1b34</load_address>
         <readonly>true</readonly>
         <run_address>0x1b34</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x1b37</load_address>
         <readonly>true</readonly>
         <run_address>0x1b37</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-99">
         <name>.data.encoder_cnt1</name>
         <load_address>0x2020012c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020012c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.data.encoder_cnt2</name>
         <load_address>0x20200130</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200130</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-79">
         <name>.data.cur_speed1</name>
         <load_address>0x20200124</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200124</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.data.cur_speed2</name>
         <load_address>0x20200128</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200128</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.data.MOTOR_A</name>
         <load_address>0x202000dc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000dc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.data.MOTOR_B</name>
         <load_address>0x20200100</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200100</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-77">
         <name>.common:aim_speed1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000d8</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-7b">
         <name>.common:aim_speed2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000da</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-113">
         <name>.common:D4</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-110">
         <name>.common:D1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-111">
         <name>.common:D2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-112">
         <name>.common:D3</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-114">
         <name>.common:D5</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000cc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-115">
         <name>.common:D6</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000d0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-116">
         <name>.common:D7</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000d4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-109">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x15b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_abbrev</name>
         <load_address>0x15b</load_address>
         <run_address>0x15b</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x2cc</load_address>
         <run_address>0x2cc</run_address>
         <size>0x181</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_abbrev</name>
         <load_address>0x44d</load_address>
         <run_address>0x44d</run_address>
         <size>0x214</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x661</load_address>
         <run_address>0x661</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_abbrev</name>
         <load_address>0x6ce</load_address>
         <run_address>0x6ce</run_address>
         <size>0x9b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_abbrev</name>
         <load_address>0x769</load_address>
         <run_address>0x769</run_address>
         <size>0x118</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_abbrev</name>
         <load_address>0x881</load_address>
         <run_address>0x881</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_abbrev</name>
         <load_address>0x8e3</load_address>
         <run_address>0x8e3</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_abbrev</name>
         <load_address>0xb69</load_address>
         <run_address>0xb69</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_abbrev</name>
         <load_address>0xd81</load_address>
         <run_address>0xd81</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_abbrev</name>
         <load_address>0xe30</load_address>
         <run_address>0xe30</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0xfa0</load_address>
         <run_address>0xfa0</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0xfd9</load_address>
         <run_address>0xfd9</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_abbrev</name>
         <load_address>0x109b</load_address>
         <run_address>0x109b</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_abbrev</name>
         <load_address>0x110b</load_address>
         <run_address>0x110b</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_abbrev</name>
         <load_address>0x1198</load_address>
         <run_address>0x1198</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_abbrev</name>
         <load_address>0x124b</load_address>
         <run_address>0x124b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0x1272</load_address>
         <run_address>0x1272</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cd"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_abbrev</name>
         <load_address>0x1299</load_address>
         <run_address>0x1299</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_abbrev</name>
         <load_address>0x12c0</load_address>
         <run_address>0x12c0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_abbrev</name>
         <load_address>0x12e7</load_address>
         <run_address>0x12e7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_abbrev</name>
         <load_address>0x130e</load_address>
         <run_address>0x130e</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_abbrev</name>
         <load_address>0x1335</load_address>
         <run_address>0x1335</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_abbrev</name>
         <load_address>0x135c</load_address>
         <run_address>0x135c</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_abbrev</name>
         <load_address>0x1381</load_address>
         <run_address>0x1381</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_abbrev</name>
         <load_address>0x13a8</load_address>
         <run_address>0x13a8</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.debug_abbrev</name>
         <load_address>0x13cd</load_address>
         <run_address>0x13cd</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb3f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_info</name>
         <load_address>0xb3f</load_address>
         <run_address>0xb3f</run_address>
         <size>0xa57</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x1596</load_address>
         <run_address>0x1596</run_address>
         <size>0xa06</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_info</name>
         <load_address>0x1f9c</load_address>
         <run_address>0x1f9c</run_address>
         <size>0x2681</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x461d</load_address>
         <run_address>0x461d</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x469d</load_address>
         <run_address>0x469d</run_address>
         <size>0x1ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_info</name>
         <load_address>0x484a</load_address>
         <run_address>0x484a</run_address>
         <size>0xe5c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_info</name>
         <load_address>0x56a6</load_address>
         <run_address>0x56a6</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_info</name>
         <load_address>0x571b</load_address>
         <run_address>0x571b</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_info</name>
         <load_address>0x888d</load_address>
         <run_address>0x888d</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x991d</load_address>
         <run_address>0x991d</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_info</name>
         <load_address>0x9d40</load_address>
         <run_address>0x9d40</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0xa484</load_address>
         <run_address>0xa484</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_info</name>
         <load_address>0xa4ca</load_address>
         <run_address>0xa4ca</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xa65c</load_address>
         <run_address>0xa65c</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xa722</load_address>
         <run_address>0xa722</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_info</name>
         <load_address>0xa89e</load_address>
         <run_address>0xa89e</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_info</name>
         <load_address>0xa98b</load_address>
         <run_address>0xa98b</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_info</name>
         <load_address>0xab32</load_address>
         <run_address>0xab32</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cd"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_info</name>
         <load_address>0xacbf</load_address>
         <run_address>0xacbf</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_info</name>
         <load_address>0xae4c</load_address>
         <run_address>0xae4c</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_info</name>
         <load_address>0xafdb</load_address>
         <run_address>0xafdb</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_info</name>
         <load_address>0xb16e</load_address>
         <run_address>0xb16e</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0xb385</load_address>
         <run_address>0xb385</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_info</name>
         <load_address>0xb51e</load_address>
         <run_address>0xb51e</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_info</name>
         <load_address>0xb6df</load_address>
         <run_address>0xb6df</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_info</name>
         <load_address>0xb86e</load_address>
         <run_address>0xb86e</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.debug_info</name>
         <load_address>0xbb68</load_address>
         <run_address>0xbb68</run_address>
         <size>0x9d</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_ranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_ranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_ranges</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x118</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x1f0</load_address>
         <run_address>0x1f0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_ranges</name>
         <load_address>0x208</load_address>
         <run_address>0x208</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_ranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-140">
         <name>.debug_ranges</name>
         <load_address>0x410</load_address>
         <run_address>0x410</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_ranges</name>
         <load_address>0x5b8</load_address>
         <run_address>0x5b8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_ranges</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x648</load_address>
         <run_address>0x648</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_ranges</name>
         <load_address>0x660</load_address>
         <run_address>0x660</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_ranges</name>
         <load_address>0x6b0</load_address>
         <run_address>0x6b0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_ranges</name>
         <load_address>0x6c8</load_address>
         <run_address>0x6c8</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_ranges</name>
         <load_address>0x700</load_address>
         <run_address>0x700</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x625</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_str</name>
         <load_address>0x625</load_address>
         <run_address>0x625</run_address>
         <size>0x780</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_str</name>
         <load_address>0xda5</load_address>
         <run_address>0xda5</run_address>
         <size>0x87b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_str</name>
         <load_address>0x1620</load_address>
         <run_address>0x1620</run_address>
         <size>0x22d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x38f0</load_address>
         <run_address>0x38f0</run_address>
         <size>0x159</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_str</name>
         <load_address>0x3a49</load_address>
         <run_address>0x3a49</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_str</name>
         <load_address>0x3bc1</load_address>
         <run_address>0x3bc1</run_address>
         <size>0x6f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_str</name>
         <load_address>0x42ba</load_address>
         <run_address>0x42ba</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_str</name>
         <load_address>0x4427</load_address>
         <run_address>0x4427</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_str</name>
         <load_address>0x61f3</load_address>
         <run_address>0x61f3</run_address>
         <size>0x1075</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_str</name>
         <load_address>0x7268</load_address>
         <run_address>0x7268</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_str</name>
         <load_address>0x748d</load_address>
         <run_address>0x748d</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x77bc</load_address>
         <run_address>0x77bc</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_str</name>
         <load_address>0x78b1</load_address>
         <run_address>0x78b1</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_str</name>
         <load_address>0x7a4c</load_address>
         <run_address>0x7a4c</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_str</name>
         <load_address>0x7bb4</load_address>
         <run_address>0x7bb4</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_str</name>
         <load_address>0x7d89</load_address>
         <run_address>0x7d89</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_frame</name>
         <load_address>0x104</load_address>
         <run_address>0x104</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_frame</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x32c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x584</load_address>
         <run_address>0x584</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x5b4</load_address>
         <run_address>0x5b4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_frame</name>
         <load_address>0x5e0</load_address>
         <run_address>0x5e0</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_frame</name>
         <load_address>0x660</load_address>
         <run_address>0x660</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_frame</name>
         <load_address>0x680</load_address>
         <run_address>0x680</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_frame</name>
         <load_address>0xa88</load_address>
         <run_address>0xa88</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_frame</name>
         <load_address>0xbb4</load_address>
         <run_address>0xbb4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_frame</name>
         <load_address>0xc44</load_address>
         <run_address>0xc44</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_frame</name>
         <load_address>0xd44</load_address>
         <run_address>0xd44</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_frame</name>
         <load_address>0xd64</load_address>
         <run_address>0xd64</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0xd9c</load_address>
         <run_address>0xd9c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0xdc4</load_address>
         <run_address>0xdc4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_frame</name>
         <load_address>0xdf4</load_address>
         <run_address>0xdf4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0xfb4</load_address>
         <run_address>0xfb4</run_address>
         <size>0x3f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x13a9</load_address>
         <run_address>0x13a9</run_address>
         <size>0x303</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_line</name>
         <load_address>0x16ac</load_address>
         <run_address>0x16ac</run_address>
         <size>0x97a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x2026</load_address>
         <run_address>0x2026</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_line</name>
         <load_address>0x20e3</load_address>
         <run_address>0x20e3</run_address>
         <size>0x1b3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_line</name>
         <load_address>0x2296</load_address>
         <run_address>0x2296</run_address>
         <size>0x2e2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_line</name>
         <load_address>0x2578</load_address>
         <run_address>0x2578</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_line</name>
         <load_address>0x26f0</load_address>
         <run_address>0x26f0</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_line</name>
         <load_address>0x3e5e</load_address>
         <run_address>0x3e5e</run_address>
         <size>0x982</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_line</name>
         <load_address>0x47e0</load_address>
         <run_address>0x47e0</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_line</name>
         <load_address>0x49bc</load_address>
         <run_address>0x49bc</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x4ed6</load_address>
         <run_address>0x4ed6</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x4f14</load_address>
         <run_address>0x4f14</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x5012</load_address>
         <run_address>0x5012</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x50d2</load_address>
         <run_address>0x50d2</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_line</name>
         <load_address>0x529a</load_address>
         <run_address>0x529a</run_address>
         <size>0x69</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_line</name>
         <load_address>0x5303</load_address>
         <run_address>0x5303</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_line</name>
         <load_address>0x540a</load_address>
         <run_address>0x540a</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cd"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_line</name>
         <load_address>0x54ea</load_address>
         <run_address>0x54ea</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0x55c6</load_address>
         <run_address>0x55c6</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_line</name>
         <load_address>0x567e</load_address>
         <run_address>0x567e</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0x573a</load_address>
         <run_address>0x573a</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_line</name>
         <load_address>0x5801</load_address>
         <run_address>0x5801</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_line</name>
         <load_address>0x58a5</load_address>
         <run_address>0x58a5</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_line</name>
         <load_address>0x59a9</load_address>
         <run_address>0x59a9</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_line</name>
         <load_address>0x5a62</load_address>
         <run_address>0x5a62</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_loc</name>
         <load_address>0x1a3a</load_address>
         <run_address>0x1a3a</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_loc</name>
         <load_address>0x1e4e</load_address>
         <run_address>0x1e4e</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_loc</name>
         <load_address>0x1f26</load_address>
         <run_address>0x1f26</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x234a</load_address>
         <run_address>0x234a</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_loc</name>
         <load_address>0x24b6</load_address>
         <run_address>0x24b6</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_loc</name>
         <load_address>0x2525</load_address>
         <run_address>0x2525</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_loc</name>
         <load_address>0x268c</load_address>
         <run_address>0x268c</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cd"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_aranges</name>
         <load_address>0x110</load_address>
         <run_address>0x110</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_aranges</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1a30</size>
         <contents>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-d3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1b40</load_address>
         <run_address>0x1b40</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-1a3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1af0</load_address>
         <run_address>0x1af0</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-14f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-16c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000dc</run_address>
         <size>0x58</size>
         <contents>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-7d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xdc</size>
         <contents>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-109"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1a7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-163" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-164" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-165" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-166" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-167" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-168" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-16a" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-186" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13dc</size>
         <contents>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-1a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-188" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbc05</size>
         <contents>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-1a8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18a" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x728</size>
         <contents>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-c2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18c" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7ec8</size>
         <contents>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-118"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18e" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe24</size>
         <contents>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-d6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-190" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5b02</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-c3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-192" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x26b2</size>
         <contents>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-d9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-19c" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x158</size>
         <contents>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a6" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1b2" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b88</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1b3" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x134</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1b4" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1b88</used_space>
         <unused_space>0x1e478</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x1a30</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1af0</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1b40</start_address>
               <size>0x48</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1b88</start_address>
               <size>0x1e478</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x334</used_space>
         <unused_space>0x7ccc</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-168"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-16a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xdc</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202000dc</start_address>
               <size>0x58</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200134</start_address>
               <size>0x7ccc</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1b40</load_address>
            <load_size>0x20</load_size>
            <run_address>0x202000dc</run_address>
            <run_size>0x58</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x1b6c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xdc</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1b74</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1b84</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1b84</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1b60</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1b6c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-44">
         <name>aim_speed1</name>
         <value>0x202000d8</value>
      </symbol>
      <symbol id="sm-45">
         <name>aim_speed2</name>
         <value>0x202000da</value>
      </symbol>
      <symbol id="sm-46">
         <name>Trailing_detection</name>
         <value>0x9fd</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-47">
         <name>D4</name>
         <value>0x202000c8</value>
      </symbol>
      <symbol id="sm-48">
         <name>D1</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-49">
         <name>D2</name>
         <value>0x202000c0</value>
      </symbol>
      <symbol id="sm-4a">
         <name>D3</name>
         <value>0x202000c4</value>
      </symbol>
      <symbol id="sm-4b">
         <name>D5</name>
         <value>0x202000cc</value>
      </symbol>
      <symbol id="sm-4c">
         <name>D6</name>
         <value>0x202000d0</value>
      </symbol>
      <symbol id="sm-4d">
         <name>D7</name>
         <value>0x202000d4</value>
      </symbol>
      <symbol id="sm-4e">
         <name>Veer_Trailing</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-70">
         <name>init_encoder</name>
         <value>0x19bd</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-71">
         <name>calc_speed</name>
         <value>0x16dd</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-72">
         <name>encoder_cnt1</name>
         <value>0x2020012c</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-73">
         <name>cur_speed1</name>
         <value>0x20200124</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-74">
         <name>encoder_cnt2</name>
         <value>0x20200130</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-75">
         <name>cur_speed2</name>
         <value>0x20200128</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-76">
         <name>calc_encoder_cnt</name>
         <value>0xaf5</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-8d">
         <name>main</name>
         <value>0x172d</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-8e">
         <name>SysTick_Handler</name>
         <value>0x11f7</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-8f">
         <name>GROUP1_IRQHandler</name>
         <value>0x1acf</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-90">
         <name>TIMA1_IRQHandler</name>
         <value>0xf3d</value>
         <object_component_ref idref="oc-3a"/>
      </symbol>
      <symbol id="sm-117">
         <name>SYSCFG_DL_init</name>
         <value>0x16b5</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-118">
         <name>SYSCFG_DL_initPower</name>
         <value>0x12d9</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-119">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x7c9</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-11a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1401</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-11b">
         <name>SYSCFG_DL_TB6612_PWM_init</name>
         <value>0xfd5</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-11c">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x15d5</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-11d">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x1aa5</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-11e">
         <name>gTIMER_0Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-129">
         <name>Default_Handler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12a">
         <name>Reset_Handler</name>
         <value>0x1ae5</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-12b">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-12c">
         <name>NMI_Handler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12d">
         <name>HardFault_Handler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12e">
         <name>SVC_Handler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12f">
         <name>PendSV_Handler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-130">
         <name>GROUP0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-131">
         <name>TIMG8_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-132">
         <name>UART3_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-133">
         <name>ADC0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-134">
         <name>ADC1_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-135">
         <name>CANFD0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-136">
         <name>DAC0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-137">
         <name>SPI0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-138">
         <name>SPI1_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-139">
         <name>UART1_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13a">
         <name>UART2_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13b">
         <name>UART0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13c">
         <name>TIMG0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13d">
         <name>TIMG6_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13e">
         <name>TIMA0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13f">
         <name>TIMG7_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-140">
         <name>TIMG12_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-141">
         <name>I2C0_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-142">
         <name>I2C1_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-143">
         <name>AES_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-144">
         <name>RTC_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>DMA_IRQHandler</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>PID_Calc</name>
         <value>0x675</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-14f">
         <name>MOTOR_A</name>
         <value>0x202000dc</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-150">
         <name>MOTOR_B</name>
         <value>0x20200100</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-168">
         <name>init_moto</name>
         <value>0x1ab5</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-169">
         <name>Motor_SetSPeed</name>
         <value>0xe85</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-16a">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-16b">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-16c">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-16d">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-16e">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-16f">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-170">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-171">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-172">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-17b">
         <name>DL_Common_delayCycles</name>
         <value>0x1ac5</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-197">
         <name>DL_Timer_setClockConfig</name>
         <value>0x186d</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-198">
         <name>DL_Timer_initTimerMode</name>
         <value>0xbe9</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-199">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x1a95</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-19a">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x1851</value>
         <object_component_ref idref="oc-148"/>
      </symbol>
      <symbol id="sm-19b">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1961</value>
         <object_component_ref idref="oc-147"/>
      </symbol>
      <symbol id="sm-19c">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x8f9</value>
         <object_component_ref idref="oc-145"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0xcd1</value>
         <object_component_ref idref="oc-13b"/>
      </symbol>
      <symbol id="sm-1ab">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x1381</value>
         <object_component_ref idref="oc-142"/>
      </symbol>
      <symbol id="sm-1b6">
         <name>_c_int00_noargs</name>
         <value>0x1705</value>
         <object_component_ref idref="oc-56"/>
      </symbol>
      <symbol id="sm-1b7">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x14b5</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-1cb">
         <name>_system_pre_init</name>
         <value>0x1ae9</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-1d6">
         <name>__TI_zero_init_nomemset</name>
         <value>0x19d3</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1df">
         <name>__TI_decompress_none</name>
         <value>0x1a71</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-1ea">
         <name>__TI_decompress_lzss</name>
         <value>0x11f9</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1f4">
         <name>abort</name>
         <value>0x1aed</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>C$$EXIT</name>
         <value>0x1aec</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>__aeabi_fadd</name>
         <value>0xdb7</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>__addsf3</name>
         <value>0xdb7</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>__aeabi_fsub</name>
         <value>0xdad</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-200">
         <name>__subsf3</name>
         <value>0xdad</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-206">
         <name>__aeabi_fmul</name>
         <value>0x1065</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-207">
         <name>__mulsf3</name>
         <value>0x1065</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-20d">
         <name>__aeabi_fdiv</name>
         <value>0x1175</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-20e">
         <name>__divsf3</name>
         <value>0x1175</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-214">
         <name>__aeabi_f2iz</name>
         <value>0x1569</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-215">
         <name>__fixsfsi</name>
         <value>0x1569</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-21b">
         <name>__aeabi_i2f</name>
         <value>0x143d</value>
         <object_component_ref idref="oc-66"/>
      </symbol>
      <symbol id="sm-21c">
         <name>__floatsisf</name>
         <value>0x143d</value>
         <object_component_ref idref="oc-66"/>
      </symbol>
      <symbol id="sm-222">
         <name>__aeabi_fcmpeq</name>
         <value>0x1275</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-223">
         <name>__aeabi_fcmplt</name>
         <value>0x1289</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-224">
         <name>__aeabi_fcmple</name>
         <value>0x129d</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-225">
         <name>__aeabi_fcmpge</name>
         <value>0x12b1</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-226">
         <name>__aeabi_fcmpgt</name>
         <value>0x12c5</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-22c">
         <name>__aeabi_memcpy</name>
         <value>0x1ad9</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-22d">
         <name>__aeabi_memcpy4</name>
         <value>0x1ad9</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-22e">
         <name>__aeabi_memcpy8</name>
         <value>0x1ad9</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-237">
         <name>__eqsf2</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-238">
         <name>__lesf2</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-239">
         <name>__ltsf2</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-23a">
         <name>__nesf2</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-23b">
         <name>__cmpsf2</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-23c">
         <name>__gtsf2</name>
         <value>0x1479</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-23d">
         <name>__gesf2</name>
         <value>0x1479</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-243">
         <name>__muldsi3</name>
         <value>0x152d</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-24c">
         <name>TI_memcpy_small</name>
         <value>0x1a5f</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-24d">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-250">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-251">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
