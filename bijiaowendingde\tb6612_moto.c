 /******************** (C) COPYRIGHT 2025   B站：电子大师兄 ****************************
* 文件名    : tb6612_moto.c
* 作者      : 电子大师兄（https://space.bilibili.com/568487373）
* 版本      : V1.0
* 时间      : 2025-7-23
* 描述      : 电机驱动模块
*******************************************************************************/
#include "tb6612_moto.h"

#define MAX_PWM   (3200)
#define LIMIT(a,x,b)  if(x<(a))x=(a);else if(x>(b))x=(b);

#define DIR_STOP       (0)
#define DIR_FORWARD    (1)
#define DIR_BACKWARD   (2)

uint8_t g_dir1 = DIR_FORWARD;
uint32_t g_pwm1 = 0;    //0~3200
uint8_t g_dir2 = DIR_FORWARD;
uint32_t g_pwm2 = 0;    //0~3200

/*******************************************************************************
* 函数名 : init_moto
* 描述    : 电机驱动初始化
* 作者    : 电子大师兄
* 输入    : None
* 输出    : None
* 返回    : None
*******************************************************************************/
void init_moto(void)
{
    DL_TimerG_startCounter(TB6612_PWM_INST);
}

void Motor_SetSPeed(int  Speed1,int  Speed2)
{   
	
   if(Speed1>=0)
	{
	DL_GPIO_setPins(TB6612_IO_PORT, TB6612_IO_AIN2_PIN);
        DL_GPIO_clearPins(TB6612_IO_PORT, TB6612_IO_AIN1_PIN);
        DL_TimerG_setCaptureCompareValue(TB6612_PWM_INST,Speed1,GPIO_TB6612_PWM_C0_IDX);
	}
	else 
	{
	   
        DL_GPIO_setPins(TB6612_IO_PORT, TB6612_IO_AIN1_PIN);
        DL_GPIO_clearPins(TB6612_IO_PORT, TB6612_IO_AIN2_PIN);
        DL_TimerG_setCaptureCompareValue(TB6612_PWM_INST,-Speed1,GPIO_TB6612_PWM_C0_IDX);
	}
	if(Speed2>=0)
	{
        DL_GPIO_setPins(TB6612_IO_PORT, TB6612_IO_BIN2_PIN);  // 修改：使用BIN2而不是BIN1
        DL_GPIO_clearPins(TB6612_IO_PORT, TB6612_IO_BIN1_PIN); // 修改：使用BIN1而不是BIN2
        DL_TimerG_setCaptureCompareValue(TB6612_PWM_INST,Speed2,GPIO_TB6612_PWM_C1_IDX);
	}
	else 
	{

        DL_GPIO_setPins(TB6612_IO_PORT, TB6612_IO_BIN1_PIN);  // 修改：使用BIN1而不是BIN2
        DL_GPIO_clearPins(TB6612_IO_PORT, TB6612_IO_BIN2_PIN); // 修改：使用BIN2而不是BIN1
        DL_TimerG_setCaptureCompareValue(TB6612_PWM_INST,-Speed2,GPIO_TB6612_PWM_C1_IDX);
	}

}