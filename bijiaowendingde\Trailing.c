#include "ti_msp_dl_config.h"
#include "Trailing.h"

int D1,D2,D3,D4,D5,D6,D7;
int16_t aim_speed1;     
int16_t aim_speed2;    

/*
 * 电机测试函数
 * 用于验证电机方向是否正确
 */
void test_motors(void)
{
    // 测试1：两个轮子都向前转（增加速度确保能转动）
    aim_speed1 = 15;
    aim_speed2 = 15;
    
    // 测试2：两个轮子都向后转（注释掉，需要时可以启用）
    // aim_speed1 = -15;
    // aim_speed2 = -15;
    
    // 测试3：左转（左轮慢，右轮快）
    // aim_speed1 = 8;
    // aim_speed2 = 20;
    
    // 测试4：右转（左轮快，右轮慢）
    // aim_speed1 = 20;
    // aim_speed2 = 8;
}

/*
 * 简单循迹测试函数
 * 用于验证循迹功能是否正常工作
 */
void simple_trailing_test(void)
{
    // 读取传感器状态
    Trailing_detection();
    
    // 简单的循迹逻辑：只有中间传感器检测到黑线时直行
    if(D4 == 1)  // 中间传感器检测到黑线
    {
        aim_speed1 = 8;
        aim_speed2 = 8;
    }
    else  // 没有检测到黑线，停止
    {
        aim_speed1 = 0;
        aim_speed2 = 0;
    }
}

/* 
 * 传感器检测（7路传感器）
 */
void Trailing_detection(void)
{
    // 读取传感器状态
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_0_PIN))
    {D1=1;}
    else {D1=0;}
    
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_1_PIN))
    {D2=1;}
    else {D2=0;}
    
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_2_PIN))
    {D3=1;}
    else {D3=0;}
    
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_3_PIN))
    {D4=1;}
    else {D4=0;}
    
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_4_PIN))
    {D5=1;}
    else {D5=0;}
    
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_5_PIN))
    {D6=1;}
    else {D6=0;}
    
    if(DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_6_PIN))
    {D7=1;}
    else {D7=0;}
}

/* 
 * 差速转向寻迹（7路传感器版）- 优化版本
 */
void Veer_Trailing(void)
{     
    Trailing_detection();//刷新标志位
    
    // 7路传感器的循迹逻辑 - 优化版本
    
    // 1. 完美居中 - 只有中间传感器检测到黑线
    if(D1==0 && D2==0 && D3==0 && D4==1 && D5==0 && D6==0 && D7==0)
    {
        aim_speed1 = ZX_SPEED;
        aim_speed2 = ZX_SPEED;
    }
    
    // 2. 轻微左偏 - 左中传感器检测到
    else if(D1==0 && D2==0 && D3==1 && D4==0 && D5==0 && D6==0 && D7==0)
    {
        aim_speed1 = ZX_SPEED - MINI_GNGLE;
        aim_speed2 = ZX_SPEED + MINI_GNGLE;
    }
    
    // 3. 轻微右偏 - 右中传感器检测到
    else if(D1==0 && D2==0 && D3==0 && D4==0 && D5==1 && D6==0 && D7==0)
    {
        aim_speed1 = ZX_SPEED + MINI_GNGLE;
        aim_speed2 = ZX_SPEED - MINI_GNGLE;
    }
    
    // 4. 中等左偏 - 左外侧传感器检测到
    else if(D1==0 && D2==1 && D3==0 && D4==0 && D5==0 && D6==0 && D7==0)
    {
        aim_speed1 = ZX_SPEED - BIG_GNGLE;
        aim_speed2 = ZX_SPEED + BIG_GNGLE;
    }
    
    // 5. 中等右偏 - 右外侧传感器检测到
    else if(D1==0 && D2==0 && D3==0 && D4==0 && D5==0 && D6==1 && D7==0)
    {
        aim_speed1 = ZX_SPEED + BIG_GNGLE;
        aim_speed2 = ZX_SPEED - BIG_GNGLE;
    }
    
    // 6. 激进左转 - 最左侧传感器检测到（立即转弯）
    else if(D1==1)
    {
        // 激进左转：左轮反转，右轮全速
        aim_speed1 = -REVERSE_TURN_SPEED;  // 左轮反转
        aim_speed2 = AGGRESSIVE_TURN_SPEED;   // 右轮全速
    }
    
    // 7. 激进右转 - 最右侧传感器检测到（立即转弯）
    else if(D7==1)
    {
        // 激进右转：右轮反转，左轮全速
        aim_speed1 = AGGRESSIVE_TURN_SPEED;   // 左轮全速
        aim_speed2 = -REVERSE_TURN_SPEED;  // 右轮反转
    }
    
    // 8. 优化：左边两位传感器检测到黑线时的转弯处理
    else if((D1==1 && D2==1) || (D2==1 && D3==1) || (D1==1 && D3==1))
    {
        // 左边两位传感器检测到，进行快速左转
        if(D1==1 && D2==1)
        {
            // 最左边两位，激进左转
            aim_speed1 = -REVERSE_TURN_SPEED;  // 左轮反转
            aim_speed2 = AGGRESSIVE_TURN_SPEED;   // 右轮全速
        }
        else if(D2==1 && D3==1)
        {
            // 中间两位，中等左转
            aim_speed1 = -LIGHT_REVERSE_SPEED;  // 左轮反转
            aim_speed2 = MEDIUM_TURN_SPEED;   // 右轮加速
        }
        else
        {
            // D1和D3，激进左转
            aim_speed1 = -REVERSE_TURN_SPEED;  // 左轮反转
            aim_speed2 = AGGRESSIVE_TURN_SPEED;   // 右轮全速
        }
    }
    
    // 9. 优化：右边两位传感器检测到黑线时的转弯处理
    else if((D5==1 && D6==1) || (D6==1 && D7==1) || (D5==1 && D7==1))
    {
        // 右边两位传感器检测到，进行快速右转
        if(D6==1 && D7==1)
        {
            // 最右边两位，激进右转
            aim_speed1 = AGGRESSIVE_TURN_SPEED;   // 左轮全速
            aim_speed2 = -REVERSE_TURN_SPEED;  // 右轮反转
        }
        else if(D5==1 && D6==1)
        {
            // 中间两位，中等右转
            aim_speed1 = MEDIUM_TURN_SPEED;   // 左轮加速
            aim_speed2 = -LIGHT_REVERSE_SPEED;  // 右轮反转
        }
        else
        {
            // D5和D7，激进右转
            aim_speed1 = AGGRESSIVE_TURN_SPEED;   // 左轮全速
            aim_speed2 = -REVERSE_TURN_SPEED;  // 右轮反转
        }
    }
    
    // 10. 复合检测情况 - 优化处理
    // 左偏复合检测（多个左侧传感器）
    else if((D1==1 || D2==1 || D3==1) && D4==0 && D5==0 && D6==0 && D7==0)
    {
        // 计算左偏程度
        int left_count = D1 + D2 + D3;
        if(left_count >= 2)  // 多个左侧传感器检测到，激进转弯
        {
            // 激进转弯：左轮反转，右轮全速
            aim_speed1 = -REVERSE_TURN_SPEED;  // 左轮反转
            aim_speed2 = AGGRESSIVE_TURN_SPEED;   // 右轮全速
        }
        else
        {
            // 普通偏离修正
            int deviation = left_count * 4;
            aim_speed1 = ZX_SPEED - deviation;
            aim_speed2 = ZX_SPEED + deviation;
        }
    }
    
    // 右偏复合检测（多个右侧传感器）
    else if(D1==0 && D2==0 && D3==0 && D4==0 && (D5==1 || D6==1 || D7==1))
    {
        // 计算右偏程度
        int right_count = D5 + D6 + D7;
        if(right_count >= 2)  // 多个右侧传感器检测到，激进转弯
        {
            // 激进转弯：右轮反转，左轮全速
            aim_speed1 = AGGRESSIVE_TURN_SPEED;   // 左轮全速
            aim_speed2 = -REVERSE_TURN_SPEED;  // 右轮反转
        }
        else
        {
            // 普通偏离修正
            int deviation = right_count * 4;
            aim_speed1 = ZX_SPEED + deviation;
            aim_speed2 = ZX_SPEED - deviation;
        }
    }
    
    // 11. 十字路口或T字路口检测
    else if(D1==1 && D2==1 && D3==1 && D4==1 && D5==1 && D6==1 && D7==1)
    {
        // 检测到十字路口，保持当前方向直行
        aim_speed1 = ZX_SPEED;
        aim_speed2 = ZX_SPEED;
    }
    
    // 12. 其他复合情况 - 简化处理
    else if(D1==1 || D2==1 || D3==1 || D4==1 || D5==1 || D6==1 || D7==1)
    {
        // 计算整体偏离
        int left_sum = D1 + D2 + D3;
        int right_sum = D5 + D6 + D7;
        int center = D4;
        
        if(left_sum > right_sum)
        {
            // 左偏
            int deviation = (left_sum - right_sum) * 3;
            aim_speed1 = ZX_SPEED - deviation;
            aim_speed2 = ZX_SPEED + deviation;
        }
        else if(right_sum > left_sum)
        {
            // 右偏
            int deviation = (right_sum - left_sum) * 3;
            aim_speed1 = ZX_SPEED + deviation;
            aim_speed2 = ZX_SPEED - deviation;
        }
        else
        {
            // 居中或轻微偏离
            aim_speed1 = ZX_SPEED;
            aim_speed2 = ZX_SPEED;
        }
    }
    
    // 13. 完全偏离黑线的情况 - 保持当前方向
    else
    {
        // 所有传感器都没有检测到黑线，保持当前速度
        aim_speed1 = ZX_SPEED;
        aim_speed2 = ZX_SPEED;
    }
}

/*
 * 直角转弯测试函数
 * 用于测试直角转弯功能
 */
void right_angle_test(void)
{
    // 读取传感器状态
    Trailing_detection();
    
    // 直角转弯测试逻辑
    if(D1 == 1)  // 最左侧传感器检测到
    {
        // 左直角转弯：左轮停止，右轮全速
        aim_speed1 = 0;
        aim_speed2 = 20;
    }
    else if(D7 == 1)  // 最右侧传感器检测到
    {
        // 右直角转弯：右轮停止，左轮全速
        aim_speed1 = 20;
        aim_speed2 = 0;
    }
    else if(D1 == 1 && D2 == 1)  // 多个左侧传感器
    {
        // 激进左转
        aim_speed1 = 5;
        aim_speed2 = 25;
    }
    else if(D6 == 1 && D7 == 1)  // 多个右侧传感器
    {
        // 激进右转
        aim_speed1 = 25;
        aim_speed2 = 5;
    }
    else if(D4 == 1)  // 中间传感器
    {
        // 直行
        aim_speed1 = 10;
        aim_speed2 = 10;
    }
    else
    {
        // 停止
        aim_speed1 = 0;
        aim_speed2 = 0;
    }
}

/*
 * 传感器调试函数
 * 用于诊断传感器检测问题
 */
void sensor_debug(void)
{
    // 读取传感器状态
    Trailing_detection();
    
    // 根据传感器状态设置不同的速度，用于调试
    if(D1 == 1)  // 最左侧传感器检测到
    {
        // 左转调试
        aim_speed1 = 5;
        aim_speed2 = 15;
    }
    else if(D7 == 1)  // 最右侧传感器检测到
    {
        // 右转调试
        aim_speed1 = 15;
        aim_speed2 = 5;
    }
    else if(D4 == 1)  // 中间传感器检测到
    {
        // 直行调试
        aim_speed1 = 10;
        aim_speed2 = 10;
    }
    else if(D1 == 1 || D2 == 1 || D3 == 1)  // 任何左侧传感器检测到
    {
        // 左偏调试
        aim_speed1 = 8;
        aim_speed2 = 12;
    }
    else if(D5 == 1 || D6 == 1 || D7 == 1)  // 任何右侧传感器检测到
    {
        // 右偏调试
        aim_speed1 = 12;
        aim_speed2 = 8;
    }
    else
    {
        // 没有检测到黑线，停止
        aim_speed1 = 0;
        aim_speed2 = 0;
    }
}

/*
 * 优化转弯函数 - 专门处理两位传感器的转弯逻辑
 * 当左边或右边两位传感器识别到黑线时进行智能转弯
 */
void optimized_turn_trailing(void)
{
    // 读取传感器状态
    Trailing_detection();
    
    // 1. 完美居中 - 只有中间传感器检测到黑线
    if(D1==0 && D2==0 && D3==0 && D4==1 && D5==0 && D6==0 && D7==0)
    {
        aim_speed1 = ZX_SPEED;
        aim_speed2 = ZX_SPEED;
    }
    
    // 2. 左边两位传感器检测到黑线 - 智能左转
    else if((D1==1 && D2==1) || (D2==1 && D3==1) || (D1==1 && D3==1))
    {
        if(D1==1 && D2==1)
        {
            // 最左边两位，激进左转
            aim_speed1 = -REVERSE_TURN_SPEED;  // 左轮反转
            aim_speed2 = AGGRESSIVE_TURN_SPEED;   // 右轮全速
        }
        else if(D2==1 && D3==1)
        {
            // 中间两位，中等左转
            aim_speed1 = -LIGHT_REVERSE_SPEED;  // 左轮反转
            aim_speed2 = MEDIUM_TURN_SPEED;   // 右轮加速
        }
        else
        {
            // D1和D3，激进左转
            aim_speed1 = -REVERSE_TURN_SPEED;  // 左轮反转
            aim_speed2 = AGGRESSIVE_TURN_SPEED;   // 右轮全速
        }
    }
    
    // 3. 右边两位传感器检测到黑线 - 智能右转
    else if((D5==1 && D6==1) || (D6==1 && D7==1) || (D5==1 && D7==1))
    {
        if(D6==1 && D7==1)
        {
            // 最右边两位，激进右转
            aim_speed1 = AGGRESSIVE_TURN_SPEED;   // 左轮全速
            aim_speed2 = -REVERSE_TURN_SPEED;  // 右轮反转
        }
        else if(D5==1 && D6==1)
        {
            // 中间两位，中等右转
            aim_speed1 = MEDIUM_TURN_SPEED;   // 左轮加速
            aim_speed2 = -LIGHT_REVERSE_SPEED;  // 右轮反转
        }
        else
        {
            // D5和D7，激进右转
            aim_speed1 = AGGRESSIVE_TURN_SPEED;   // 左轮全速
            aim_speed2 = -REVERSE_TURN_SPEED;  // 右轮反转
        }
    }
    
    // 4. 单个传感器检测到 - 轻微修正
    else if(D1==1 || D2==1 || D3==1 || D5==1 || D6==1 || D7==1)
    {
        if(D1==1 || D2==1 || D3==1)
        {
            // 左侧单个传感器，轻微左转
            int deviation = (D1 * 3 + D2 * 2 + D3 * 1) * 2;
            aim_speed1 = ZX_SPEED - deviation;
            aim_speed2 = ZX_SPEED + deviation;
        }
        else
        {
            // 右侧单个传感器，轻微右转
            int deviation = (D5 * 1 + D6 * 2 + D7 * 3) * 2;
            aim_speed1 = ZX_SPEED + deviation;
            aim_speed2 = ZX_SPEED - deviation;
        }
    }
    
    // 5. 十字路口检测
    else if(D1==1 && D2==1 && D3==1 && D4==1 && D5==1 && D6==1 && D7==1)
    {
        // 检测到十字路口，保持当前方向直行
        aim_speed1 = ZX_SPEED;
        aim_speed2 = ZX_SPEED;
    }
    
    // 6. 完全偏离黑线 - 保持当前方向
    else
    {
        // 所有传感器都没有检测到黑线，保持当前速度
        aim_speed1 = ZX_SPEED;
        aim_speed2 = ZX_SPEED;
    }
}

// 静态变量用于传感器防抖和转弯一致性
static int left_turn_count = 0;
static int right_turn_count = 0;
static int straight_count = 0;
static int16_t last_aim_speed1 = 0;
static int16_t last_aim_speed2 = 0;

/*
 * 稳定转弯函数 - 解决转弯不一致问题
 * 包含传感器防抖、转弯一致性控制和速度平滑
 */
void stable_turn_trailing(void)
{
    // 读取传感器状态
    Trailing_detection();
    
    // 临时速度变量
    int16_t temp_speed1 = 0;
    int16_t temp_speed2 = 0;
    
    // 1. 完美居中 - 只有中间传感器检测到黑线
    if(D1==0 && D2==0 && D3==0 && D4==1 && D5==0 && D6==0 && D7==0)
    {
        temp_speed1 = ZX_SPEED;
        temp_speed2 = ZX_SPEED;
        straight_count++;
        left_turn_count = 0;
        right_turn_count = 0;
    }
    
    // 2. 左边两位传感器检测到黑线 - 智能左转（带防抖）
    else if((D1==1 && D2==1) || (D2==1 && D3==1) || (D1==1 && D3==1))
    {
        left_turn_count++;
        right_turn_count = 0;
        straight_count = 0;
        
        // 防抖：连续检测到左转才执行
        if(left_turn_count >= SENSOR_DEBOUNCE_COUNT)
        {
            if(D1==1 && D2==1)
            {
                // 最左边两位，激进左转
                temp_speed1 = -REVERSE_TURN_SPEED;
                temp_speed2 = AGGRESSIVE_TURN_SPEED;
            }
            else if(D2==1 && D3==1)
            {
                // 中间两位，中等左转
                temp_speed1 = -LIGHT_REVERSE_SPEED;
                temp_speed2 = MEDIUM_TURN_SPEED;
            }
            else
            {
                // D1和D3，激进左转
                temp_speed1 = -REVERSE_TURN_SPEED;
                temp_speed2 = AGGRESSIVE_TURN_SPEED;
            }
        }
        else
        {
            // 防抖期间保持当前状态
            temp_speed1 = last_aim_speed1;
            temp_speed2 = last_aim_speed2;
        }
    }
    
    // 3. 右边两位传感器检测到黑线 - 智能右转（带防抖）
    else if((D5==1 && D6==1) || (D6==1 && D7==1) || (D5==1 && D7==1))
    {
        right_turn_count++;
        left_turn_count = 0;
        straight_count = 0;
        
        // 防抖：连续检测到右转才执行
        if(right_turn_count >= SENSOR_DEBOUNCE_COUNT)
        {
            if(D6==1 && D7==1)
            {
                // 最右边两位，激进右转
                temp_speed1 = AGGRESSIVE_TURN_SPEED;
                temp_speed2 = -REVERSE_TURN_SPEED;
            }
            else if(D5==1 && D6==1)
            {
                // 中间两位，中等右转
                temp_speed1 = MEDIUM_TURN_SPEED;
                temp_speed2 = -LIGHT_REVERSE_SPEED;
            }
            else
            {
                // D5和D7，激进右转
                temp_speed1 = AGGRESSIVE_TURN_SPEED;
                temp_speed2 = -REVERSE_TURN_SPEED;
            }
        }
        else
        {
            // 防抖期间保持当前状态
            temp_speed1 = last_aim_speed1;
            temp_speed2 = last_aim_speed2;
        }
    }
    
    // 4. 单个传感器检测到 - 轻微修正
    else if(D1==1 || D2==1 || D3==1 || D5==1 || D6==1 || D7==1)
    {
        // 重置计数器
        left_turn_count = 0;
        right_turn_count = 0;
        straight_count = 0;
        
        if(D1==1 || D2==1 || D3==1)
        {
            // 左侧单个传感器，轻微左转
            int deviation = (D1 * 3 + D2 * 2 + D3 * 1) * 2;
            temp_speed1 = ZX_SPEED - deviation;
            temp_speed2 = ZX_SPEED + deviation;
        }
        else
        {
            // 右侧单个传感器，轻微右转
            int deviation = (D5 * 1 + D6 * 2 + D7 * 3) * 2;
            temp_speed1 = ZX_SPEED + deviation;
            temp_speed2 = ZX_SPEED - deviation;
        }
    }
    
    // 5. 十字路口检测
    else if(D1==1 && D2==1 && D3==1 && D4==1 && D5==1 && D6==1 && D7==1)
    {
        temp_speed1 = ZX_SPEED;
        temp_speed2 = ZX_SPEED;
        straight_count++;
        left_turn_count = 0;
        right_turn_count = 0;
    }
    
    // 6. 完全偏离黑线 - 保持当前方向
    else
    {
        temp_speed1 = ZX_SPEED;
        temp_speed2 = ZX_SPEED;
        straight_count++;
        left_turn_count = 0;
        right_turn_count = 0;
    }
    
    // 速度平滑处理：避免速度突变
    aim_speed1 = (int16_t)(SPEED_SMOOTH_FACTOR * last_aim_speed1 + (1.0f - SPEED_SMOOTH_FACTOR) * temp_speed1);
    aim_speed2 = (int16_t)(SPEED_SMOOTH_FACTOR * last_aim_speed2 + (1.0f - SPEED_SMOOTH_FACTOR) * temp_speed2);
    
    // 更新上一次的速度
    last_aim_speed1 = aim_speed1;
    last_aim_speed2 = aim_speed2;
}

// 静态变量用于防甩尾转弯
static int16_t smooth_speed1 = 0;
static int16_t smooth_speed2 = 0;
static int turn_direction = 0; // 0=直行, 1=左转, 2=右转

/*
 * 防甩尾转弯函数 - 专门解决转弯时的甩尾问题
 * 使用渐进式转弯和更强的平滑处理
 */
void anti_fishtail_turn_trailing(void)
{
    // 读取传感器状态
    Trailing_detection();
    
    // 临时速度变量
    int16_t temp_speed1 = 0;
    int16_t temp_speed2 = 0;
    int new_turn_direction = 0;
    
    // 1. 完美居中 - 只有中间传感器检测到黑线
    if(D1==0 && D2==0 && D3==0 && D4==1 && D5==0 && D6==0 && D7==0)
    {
        temp_speed1 = ZX_SPEED;
        temp_speed2 = ZX_SPEED;
        new_turn_direction = 0;
    }
    
    // 2. 左边两位传感器检测到黑线 - 渐进式左转
    else if((D1==1 && D2==1) || (D2==1 && D3==1) || (D1==1 && D3==1))
    {
        new_turn_direction = 1;
        
        if(D1==1 && D2==1)
        {
            // 最左边两位，渐进式左转（降低激进程度）
            temp_speed1 = -REVERSE_TURN_SPEED * 0.8;  // 减少反转力度
            temp_speed2 = AGGRESSIVE_TURN_SPEED * 0.9; // 减少前进力度
        }
        else if(D2==1 && D3==1)
        {
            // 中间两位，温和左转
            temp_speed1 = -LIGHT_REVERSE_SPEED * 0.7;  // 更温和的反转
            temp_speed2 = MEDIUM_TURN_SPEED * 0.8;     // 更温和的前进
        }
        else
        {
            // D1和D3，渐进式左转
            temp_speed1 = -REVERSE_TURN_SPEED * 0.8;
            temp_speed2 = AGGRESSIVE_TURN_SPEED * 0.9;
        }
    }
    
    // 3. 右边两位传感器检测到黑线 - 渐进式右转
    else if((D5==1 && D6==1) || (D6==1 && D7==1) || (D5==1 && D7==1))
    {
        new_turn_direction = 2;
        
        if(D6==1 && D7==1)
        {
            // 最右边两位，渐进式右转
            temp_speed1 = AGGRESSIVE_TURN_SPEED * 0.9;
            temp_speed2 = -REVERSE_TURN_SPEED * 0.8;
        }
        else if(D5==1 && D6==1)
        {
            // 中间两位，温和右转
            temp_speed1 = MEDIUM_TURN_SPEED * 0.8;
            temp_speed2 = -LIGHT_REVERSE_SPEED * 0.7;
        }
        else
        {
            // D5和D7，渐进式右转
            temp_speed1 = AGGRESSIVE_TURN_SPEED * 0.9;
            temp_speed2 = -REVERSE_TURN_SPEED * 0.8;
        }
    }
    
    // 4. 单个传感器检测到 - 轻微修正
    else if(D1==1 || D2==1 || D3==1 || D5==1 || D6==1 || D7==1)
    {
        new_turn_direction = 0;
        
        if(D1==1 || D2==1 || D3==1)
        {
            // 左侧单个传感器，轻微左转
            int deviation = (D1 * 3 + D2 * 2 + D3 * 1) * 1; // 减少修正力度
            temp_speed1 = ZX_SPEED - deviation;
            temp_speed2 = ZX_SPEED + deviation;
        }
        else
        {
            // 右侧单个传感器，轻微右转
            int deviation = (D5 * 1 + D6 * 2 + D7 * 3) * 1; // 减少修正力度
            temp_speed1 = ZX_SPEED + deviation;
            temp_speed2 = ZX_SPEED - deviation;
        }
    }
    
    // 5. 十字路口检测
    else if(D1==1 && D2==1 && D3==1 && D4==1 && D5==1 && D6==1 && D7==1)
    {
        temp_speed1 = ZX_SPEED;
        temp_speed2 = ZX_SPEED;
        new_turn_direction = 0;
    }
    
    // 6. 完全偏离黑线 - 保持当前方向
    else
    {
        temp_speed1 = ZX_SPEED;
        temp_speed2 = ZX_SPEED;
        new_turn_direction = 0;
    }
    
    // 防甩尾处理：根据转弯方向调整平滑因子
    float current_smooth_factor = SPEED_SMOOTH_FACTOR;
    
    // 如果转弯方向改变，使用更强的平滑
    if(new_turn_direction != turn_direction)
    {
        current_smooth_factor = 0.95f; // 更强的平滑
    }
    // 如果正在转弯，使用中等平滑
    else if(new_turn_direction != 0)
    {
        current_smooth_factor = 0.9f;
    }
    
    // 速度平滑处理：避免速度突变
    aim_speed1 = (int16_t)(current_smooth_factor * smooth_speed1 + (1.0f - current_smooth_factor) * temp_speed1);
    aim_speed2 = (int16_t)(current_smooth_factor * smooth_speed2 + (1.0f - current_smooth_factor) * temp_speed2);
    
    // 更新平滑速度和转弯方向
    smooth_speed1 = aim_speed1;
    smooth_speed2 = aim_speed2;
    turn_direction = new_turn_direction;
}