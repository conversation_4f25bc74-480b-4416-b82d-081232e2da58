# 传感器问题诊断指南

## 问题现象
- 直接放在转弯处可以转弯，说明代码逻辑正确
- 正常行驶时遇到转弯处不转弯，说明传感器检测有问题

## 可能原因分析

### 1. 传感器灵敏度问题
- **传感器不够敏感**：正常行驶时检测不到黑线
- **传感器太敏感**：产生误检测，影响判断
- **传感器响应速度慢**：高速行驶时跟不上

### 2. 传感器安装问题
- **安装高度不合适**：距离地面太远或太近
- **安装角度不对**：传感器没有垂直对准地面
- **传感器排列不对称**：影响检测精度

### 3. 环境因素
- **光线条件**：环境光太强或太弱
- **地面反射**：地面材质影响红外反射
- **黑线质量**：黑线不够明显或宽度不合适

## 诊断步骤

### 步骤1：使用传感器调试模式
1. 修改 `main.c` 中的函数调用：
   ```c
   while (1) {
       sensor_debug();  // 使用传感器调试模式
   }
   ```

2. 测试效果：
   - 将小车放在黑线上，观察是否前进
   - 将小车放在转弯处，观察是否转弯
   - 将小车移开黑线，观察是否停止

### 步骤2：检查传感器状态
在调试模式下，观察以下现象：

#### 正常情况
- 中间传感器检测到黑线时：小车直行
- 最外侧传感器检测到黑线时：小车转弯
- 没有传感器检测到黑线时：小车停止

#### 异常情况
- 传感器检测不稳定：时有时无
- 传感器误检测：没有黑线也检测到
- 传感器不检测：有黑线也检测不到

### 步骤3：硬件检查

#### 传感器安装检查
1. **安装高度**：传感器距离地面5-10mm
2. **安装角度**：传感器垂直对准地面
3. **安装对称性**：7个传感器排列对称

#### 传感器清洁
1. 清洁传感器表面
2. 检查传感器是否有损坏
3. 检查传感器接线是否正常

#### 电源检查
1. 检查传感器供电电压是否稳定
2. 检查电池电量是否充足

## 解决方案

### 方案1：调整传感器安装
```bash
# 调整传感器安装高度
- 如果检测不到：降低传感器高度
- 如果误检测：提高传感器高度

# 调整传感器角度
- 确保传感器垂直对准地面
- 避免倾斜安装
```

### 方案2：优化检测逻辑
如果传感器检测不稳定，可以添加滤波：

```c
// 在 Trailing_detection() 函数中添加滤波
void Trailing_detection(void)
{
    static int D1_filter = 0, D2_filter = 0, D3_filter = 0;
    static int D4_filter = 0, D5_filter = 0, D6_filter = 0, D7_filter = 0;
    
    // 读取传感器状态
    int D1_raw = DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_0_PIN);
    int D2_raw = DL_GPIO_readPins(GPIO_HWCGQ_PORT,GPIO_HWCGQ_PIN_1_PIN);
    // ... 其他传感器
    
    // 简单滤波：连续3次检测到才认为有效
    D1_filter = (D1_filter << 1) | D1_raw;
    D1 = (D1_filter & 0x07) == 0x07 ? 1 : 0;
    
    // ... 其他传感器类似处理
}
```

### 方案3：调整检测频率
如果检测频率不够，可以增加检测次数：

```c
// 在 Veer_Trailing() 函数中多次检测
void Veer_Trailing(void)
{
    // 多次检测取平均值
    for(int i = 0; i < 3; i++) {
        Trailing_detection();
        // 短暂延时
        for(int j = 0; j < 1000; j++);
    }
    
    // 后续逻辑...
}
```

### 方案4：环境优化
1. **光线条件**：避免强光直射，保持均匀光照
2. **地面材质**：使用不反光的地面
3. **黑线质量**：使用清晰的黑线，宽度2-3cm

## 测试建议

### 测试1：静态测试
1. 将小车静止放在黑线上
2. 观察传感器检测状态
3. 验证检测逻辑是否正确

### 测试2：低速测试
1. 降低小车速度进行测试
2. 观察传感器检测是否稳定
3. 验证转弯功能是否正常

### 测试3：高速测试
1. 提高小车速度进行测试
2. 观察传感器检测是否跟得上
3. 验证转弯功能是否正常

## 常见问题排查

### 问题1：传感器检测不稳定
**可能原因**：
- 传感器安装松动
- 电源电压不稳定
- 环境光线变化

**解决方法**：
- 固定传感器安装
- 检查电源电路
- 改善环境光线

### 问题2：传感器误检测
**可能原因**：
- 传感器安装高度太低
- 地面反光
- 环境光干扰

**解决方法**：
- 提高传感器安装高度
- 使用不反光地面
- 改善环境光线

### 问题3：传感器检测不到
**可能原因**：
- 传感器安装高度太高
- 传感器损坏
- 黑线不够明显

**解决方法**：
- 降低传感器安装高度
- 检查传感器是否损坏
- 使用更明显的黑线

## 调试技巧

### 1. 使用LED指示
如果有LED，可以用LED指示传感器状态：
```c
// 用LED指示传感器状态
if(D1 == 1) LED1_ON(); else LED1_OFF();
if(D7 == 1) LED7_ON(); else LED7_OFF();
```

### 2. 使用串口输出
如果有串口，可以输出传感器状态：
```c
// 输出传感器状态
printf("D1=%d D2=%d D3=%d D4=%d D5=%d D6=%d D7=%d\n", 
       D1, D2, D3, D4, D5, D6, D7);
```

### 3. 逐步调试
1. 先调试单个传感器
2. 再调试多个传感器组合
3. 最后调试完整循迹功能

## 预期效果

经过调试后，应该达到以下效果：
- 传感器检测稳定可靠
- 正常行驶时能正确检测到转弯
- 转弯功能正常工作
- 循迹精度满足要求 