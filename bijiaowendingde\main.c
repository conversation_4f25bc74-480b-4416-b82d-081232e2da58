 /******************** (C) COPYRIGHT 2025   B站：电子大师兄 ****************************
* 文件名    : main.c
* 作者      : 电子大师兄（https://space.bilibili.com/568487373）
* 版本      : V1.0
* 时间      : 2025-7-23
* 描述      : 主逻辑文件
*******************************************************************************/

#include "ti_msp_dl_config.h"
#include "tb6612_moto.h"
#include "encoder.h"
#include "pid.h"
#include "Trailing.h"


int main(void)
{   
    SYSCFG_DL_init();
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);//开启定时器
    init_moto();
    init_encoder();
    
    // 七路传感器循迹
    while (1) {
        Veer_Trailing();
    }
}

/*******************************************************************************
* 函数名 : SysTick_Handler
* 描述    : Systick定时器中断，根据配置，每十毫秒进一次中断
* 作者    : 电子大师兄
* 输入    : None
* 输出    : None
* 返回    : None
*******************************************************************************/
void SysTick_Handler(void)
{
  
}

/*******************************************************************************
* 函数名 : GROUP1_IRQHandler
* 描述    : 外部中断，根据配置，上升和下降沿都会产生中断
* 作者    : 电子大师兄
* 输入    : None
* 输出    : None
* 返回    : None
*******************************************************************************/
void GROUP1_IRQHandler(void)
{ 
    calc_encoder_cnt();  //编码器脉冲中断，两相，上下边沿触发，得到旋转脉冲数
}


 //每10ms执行一次的定时器
void TIMER_0_INST_IRQHandler(void)
{
    switch (DL_Timer_getPendingInterrupt(TIMER_0_INST)) {
        case DL_TIMER_IIDX_ZERO:
           
    calc_speed(); 
    PID_Calc(&MOTOR_A,aim_speed1,cur_speed1);//PID计算
    PID_Calc(&MOTOR_B,aim_speed2,cur_speed2);
    Motor_SetSPeed(MOTOR_A.output,MOTOR_B.output);//发送PWM
            break;
        default:
            break;
    }
}