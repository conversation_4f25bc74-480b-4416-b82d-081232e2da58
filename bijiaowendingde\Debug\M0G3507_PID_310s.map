******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Aug  2 04:19:20 2025

OUTPUT FILE NAME:   <M0G3507_PID_310s.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001705


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001b88  0001e478  R  X
  SRAM                  20200000   00008000  00000334  00007ccc  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001b88   00001b88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001a30   00001a30    r-x .text
  00001af0    00001af0    00000050   00000050    r-- .rodata
  00001b40    00001b40    00000048   00000048    r-- .cinit
20200000    20200000    00000134   00000000    rw-
  20200000    20200000    000000dc   00000000    rw- .bss
  202000dc    202000dc    00000058   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001a30     
                  000000c0    000005b4     Trailing.o (.text.Veer_Trailing)
                  00000674    00000154     pid.o (.text.PID_Calc)
                  000007c8    00000130     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000008f8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000009fc    000000f8     Trailing.o (.text.Trailing_detection)
                  00000af4    000000f4     encoder.o (.text.calc_encoder_cnt)
                  00000be8    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00000cd0    000000dc                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000dac    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000e84    000000b8     tb6612_moto.o (.text.Motor_SetSPeed)
                  00000f3c    00000098     main.o (.text.TIMA1_IRQHandler)
                  00000fd4    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_TB6612_PWM_init)
                  00001064    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000010f0    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001174    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000011f6    00000002     main.o (.text.SysTick_Handler)
                  000011f8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001274    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000012d6    00000002     --HOLE-- [fill = 0]
                  000012d8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001330    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00001380    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000013c4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001400    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000143c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001478    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000014b4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000014f0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000152a    00000002     --HOLE-- [fill = 0]
                  0000152c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00001566    00000002     --HOLE-- [fill = 0]
                  00001568    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000015a0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000015d4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00001608    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00001634    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00001660    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  0000168c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000016b4    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000016dc    00000028     encoder.o (.text.calc_speed)
                  00001704    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000172c    00000022     main.o (.text.main)
                  0000174e    00000002     --HOLE-- [fill = 0]
                  00001750    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00001770    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  0000178c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000017a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000017c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000017e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000017fc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001818    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001834    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00001850    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000186c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001888    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000018a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000018b8    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000018d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000018e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001900    00000018     tb6612_moto.o (.text.DL_GPIO_setPins)
                  00001918    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001930    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001948    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001960    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001978    00000018     tb6612_moto.o (.text.DL_Timer_startCounter)
                  00001990    00000016     Trailing.o (.text.DL_GPIO_readPins)
                  000019a6    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000019bc    00000016     encoder.o (.text.init_encoder)
                  000019d2    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000019e8    00000014     tb6612_moto.o (.text.DL_GPIO_clearPins)
                  000019fc    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001a10    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001a24    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001a38    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00001a4c    00000012     main.o (.text.DL_Timer_getPendingInterrupt)
                  00001a5e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001a70    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00001a82    00000002     --HOLE-- [fill = 0]
                  00001a84    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001a94    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001aa4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001ab4    00000010     tb6612_moto.o (.text.init_moto)
                  00001ac4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001ace    00000008     main.o (.text.GROUP1_IRQHandler)
                  00001ad6    00000002     --HOLE-- [fill = 0]
                  00001ad8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001ae0    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001ae4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001ae8    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001aec    00000004            : exit.c.obj (.text:abort)

.cinit     0    00001b40    00000048     
                  00001b40    00000020     (.cinit..data.load) [load image, compression = lzss]
                  00001b60    0000000c     (__TI_handler_table)
                  00001b6c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001b74    00000010     (__TI_cinit_table)
                  00001b84    00000004     --HOLE-- [fill = 0]

.rodata    0    00001af0    00000050     
                  00001af0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001b18    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00001b2c    00000008     ti_msp_dl_config.o (.rodata.gTB6612_PWMConfig)
                  00001b34    00000003     ti_msp_dl_config.o (.rodata.gTB6612_PWMClockConfig)
                  00001b37    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00001b3a    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000dc     UNINITIALIZED
                  20200000    000000bc     (.common:gTIMER_0Backup)
                  202000bc    00000004     (.common:D1)
                  202000c0    00000004     (.common:D2)
                  202000c4    00000004     (.common:D3)
                  202000c8    00000004     (.common:D4)
                  202000cc    00000004     (.common:D5)
                  202000d0    00000004     (.common:D6)
                  202000d4    00000004     (.common:D7)
                  202000d8    00000002     (.common:aim_speed1)
                  202000da    00000002     (.common:aim_speed2)

.data      0    202000dc    00000058     UNINITIALIZED
                  202000dc    00000024     pid.o (.data.MOTOR_A)
                  20200100    00000024     pid.o (.data.MOTOR_B)
                  20200124    00000004     encoder.o (.data.cur_speed1)
                  20200128    00000004     encoder.o (.data.cur_speed2)
                  2020012c    00000004     encoder.o (.data.encoder_cnt1)
                  20200130    00000004     encoder.o (.data.encoder_cnt2)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1560   74        188    
       Trailing.o                     1730   0         32     
       encoder.o                      468    0         16     
       pid.o                          340    0         72     
       tb6612_moto.o                  268    0         0      
       main.o                         258    0         0      
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         4632   266       308    
                                                              
    D:/diansai/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         886    0         0      
                                                              
    D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         290    0         0      
                                                              
    D:\diansai\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         884    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      68        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   6692   334       820    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001b74 records: 2, size/record: 8, table size: 16
	.data: load addr=00001b40, load size=00000020 bytes, run addr=202000dc, run size=00000058 bytes, compression=lzss
	.bss: load addr=00001b6c, load size=00000008 bytes, run addr=20200000, run size=000000dc bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001b60 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001ae1  ADC0_IRQHandler                      
00001ae1  ADC1_IRQHandler                      
00001ae1  AES_IRQHandler                       
00001aec  C$$EXIT                              
00001ae1  CANFD0_IRQHandler                    
202000bc  D1                                   
202000c0  D2                                   
202000c4  D3                                   
202000c8  D4                                   
202000cc  D5                                   
202000d0  D6                                   
202000d4  D7                                   
00001ae1  DAC0_IRQHandler                      
00001ac5  DL_Common_delayCycles                
00000cd1  DL_SYSCTL_configSYSPLL               
00001381  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000008f9  DL_Timer_initFourCCPWMMode           
00000be9  DL_Timer_initTimerMode               
00001851  DL_Timer_setCaptCompUpdateMethod     
00001961  DL_Timer_setCaptureCompareOutCtl     
00001a95  DL_Timer_setCaptureCompareValue      
0000186d  DL_Timer_setClockConfig              
00001ae1  DMA_IRQHandler                       
00001ae1  Default_Handler                      
00001ae1  GROUP0_IRQHandler                    
00001acf  GROUP1_IRQHandler                    
00001ae1  HardFault_Handler                    
00001ae1  I2C0_IRQHandler                      
00001ae1  I2C1_IRQHandler                      
202000dc  MOTOR_A                              
20200100  MOTOR_B                              
00000e85  Motor_SetSPeed                       
00001ae1  NMI_Handler                          
00000675  PID_Calc                             
00001ae1  PendSV_Handler                       
00001ae1  RTC_IRQHandler                       
00001ae5  Reset_Handler                        
00001ae1  SPI0_IRQHandler                      
00001ae1  SPI1_IRQHandler                      
00001ae1  SVC_Handler                          
000007c9  SYSCFG_DL_GPIO_init                  
00001401  SYSCFG_DL_SYSCTL_init                
00001aa5  SYSCFG_DL_SYSTICK_init               
00000fd5  SYSCFG_DL_TB6612_PWM_init            
000015d5  SYSCFG_DL_TIMER_0_init               
000016b5  SYSCFG_DL_init                       
000012d9  SYSCFG_DL_initPower                  
000011f7  SysTick_Handler                      
00001ae1  TIMA0_IRQHandler                     
00000f3d  TIMA1_IRQHandler                     
00001ae1  TIMG0_IRQHandler                     
00001ae1  TIMG12_IRQHandler                    
00001ae1  TIMG6_IRQHandler                     
00001ae1  TIMG7_IRQHandler                     
00001ae1  TIMG8_IRQHandler                     
00001a5f  TI_memcpy_small                      
000009fd  Trailing_detection                   
00001ae1  UART0_IRQHandler                     
00001ae1  UART1_IRQHandler                     
00001ae1  UART2_IRQHandler                     
00001ae1  UART3_IRQHandler                     
000000c1  Veer_Trailing                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001b74  __TI_CINIT_Base                      
00001b84  __TI_CINIT_Limit                     
00001b84  __TI_CINIT_Warm                      
00001b60  __TI_Handler_Table_Base              
00001b6c  __TI_Handler_Table_Limit             
000014b5  __TI_auto_init_nobinit_nopinit       
000011f9  __TI_decompress_lzss                 
00001a71  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000019d3  __TI_zero_init_nomemset              
00000db7  __addsf3                             
00001569  __aeabi_f2iz                         
00000db7  __aeabi_fadd                         
00001275  __aeabi_fcmpeq                       
000012b1  __aeabi_fcmpge                       
000012c5  __aeabi_fcmpgt                       
0000129d  __aeabi_fcmple                       
00001289  __aeabi_fcmplt                       
00001175  __aeabi_fdiv                         
00001065  __aeabi_fmul                         
00000dad  __aeabi_fsub                         
0000143d  __aeabi_i2f                          
00001ad9  __aeabi_memcpy                       
00001ad9  __aeabi_memcpy4                      
00001ad9  __aeabi_memcpy8                      
ffffffff  __binit__                            
000014f1  __cmpsf2                             
00001175  __divsf3                             
000014f1  __eqsf2                              
00001569  __fixsfsi                            
0000143d  __floatsisf                          
00001479  __gesf2                              
00001479  __gtsf2                              
000014f1  __lesf2                              
000014f1  __ltsf2                              
UNDEFED   __mpu_init                           
0000152d  __muldsi3                            
00001065  __mulsf3                             
000014f1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000dad  __subsf3                             
00001705  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00001ae9  _system_pre_init                     
00001aed  abort                                
202000d8  aim_speed1                           
202000da  aim_speed2                           
ffffffff  binit                                
00000af5  calc_encoder_cnt                     
000016dd  calc_speed                           
20200124  cur_speed1                           
20200128  cur_speed2                           
2020012c  encoder_cnt1                         
20200130  encoder_cnt2                         
20200000  gTIMER_0Backup                       
000019bd  init_encoder                         
00001ab5  init_moto                            
00000000  interruptVectors                     
0000172d  main                                 


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Veer_Trailing                        
00000200  __STACK_SIZE                         
00000675  PID_Calc                             
000007c9  SYSCFG_DL_GPIO_init                  
000008f9  DL_Timer_initFourCCPWMMode           
000009fd  Trailing_detection                   
00000af5  calc_encoder_cnt                     
00000be9  DL_Timer_initTimerMode               
00000cd1  DL_SYSCTL_configSYSPLL               
00000dad  __aeabi_fsub                         
00000dad  __subsf3                             
00000db7  __addsf3                             
00000db7  __aeabi_fadd                         
00000e85  Motor_SetSPeed                       
00000f3d  TIMA1_IRQHandler                     
00000fd5  SYSCFG_DL_TB6612_PWM_init            
00001065  __aeabi_fmul                         
00001065  __mulsf3                             
00001175  __aeabi_fdiv                         
00001175  __divsf3                             
000011f7  SysTick_Handler                      
000011f9  __TI_decompress_lzss                 
00001275  __aeabi_fcmpeq                       
00001289  __aeabi_fcmplt                       
0000129d  __aeabi_fcmple                       
000012b1  __aeabi_fcmpge                       
000012c5  __aeabi_fcmpgt                       
000012d9  SYSCFG_DL_initPower                  
00001381  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001401  SYSCFG_DL_SYSCTL_init                
0000143d  __aeabi_i2f                          
0000143d  __floatsisf                          
00001479  __gesf2                              
00001479  __gtsf2                              
000014b5  __TI_auto_init_nobinit_nopinit       
000014f1  __cmpsf2                             
000014f1  __eqsf2                              
000014f1  __lesf2                              
000014f1  __ltsf2                              
000014f1  __nesf2                              
0000152d  __muldsi3                            
00001569  __aeabi_f2iz                         
00001569  __fixsfsi                            
000015d5  SYSCFG_DL_TIMER_0_init               
000016b5  SYSCFG_DL_init                       
000016dd  calc_speed                           
00001705  _c_int00_noargs                      
0000172d  main                                 
00001851  DL_Timer_setCaptCompUpdateMethod     
0000186d  DL_Timer_setClockConfig              
00001961  DL_Timer_setCaptureCompareOutCtl     
000019bd  init_encoder                         
000019d3  __TI_zero_init_nomemset              
00001a5f  TI_memcpy_small                      
00001a71  __TI_decompress_none                 
00001a95  DL_Timer_setCaptureCompareValue      
00001aa5  SYSCFG_DL_SYSTICK_init               
00001ab5  init_moto                            
00001ac5  DL_Common_delayCycles                
00001acf  GROUP1_IRQHandler                    
00001ad9  __aeabi_memcpy                       
00001ad9  __aeabi_memcpy4                      
00001ad9  __aeabi_memcpy8                      
00001ae1  ADC0_IRQHandler                      
00001ae1  ADC1_IRQHandler                      
00001ae1  AES_IRQHandler                       
00001ae1  CANFD0_IRQHandler                    
00001ae1  DAC0_IRQHandler                      
00001ae1  DMA_IRQHandler                       
00001ae1  Default_Handler                      
00001ae1  GROUP0_IRQHandler                    
00001ae1  HardFault_Handler                    
00001ae1  I2C0_IRQHandler                      
00001ae1  I2C1_IRQHandler                      
00001ae1  NMI_Handler                          
00001ae1  PendSV_Handler                       
00001ae1  RTC_IRQHandler                       
00001ae1  SPI0_IRQHandler                      
00001ae1  SPI1_IRQHandler                      
00001ae1  SVC_Handler                          
00001ae1  TIMA0_IRQHandler                     
00001ae1  TIMG0_IRQHandler                     
00001ae1  TIMG12_IRQHandler                    
00001ae1  TIMG6_IRQHandler                     
00001ae1  TIMG7_IRQHandler                     
00001ae1  TIMG8_IRQHandler                     
00001ae1  UART0_IRQHandler                     
00001ae1  UART1_IRQHandler                     
00001ae1  UART2_IRQHandler                     
00001ae1  UART3_IRQHandler                     
00001ae5  Reset_Handler                        
00001ae9  _system_pre_init                     
00001aec  C$$EXIT                              
00001aed  abort                                
00001b60  __TI_Handler_Table_Base              
00001b6c  __TI_Handler_Table_Limit             
00001b74  __TI_CINIT_Base                      
00001b84  __TI_CINIT_Limit                     
00001b84  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gTIMER_0Backup                       
202000bc  D1                                   
202000c0  D2                                   
202000c4  D3                                   
202000c8  D4                                   
202000cc  D5                                   
202000d0  D6                                   
202000d4  D7                                   
202000d8  aim_speed1                           
202000da  aim_speed2                           
202000dc  MOTOR_A                              
20200100  MOTOR_B                              
20200124  cur_speed1                           
20200128  cur_speed2                           
2020012c  encoder_cnt1                         
20200130  encoder_cnt2                         
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[139 symbols]
