# 参数调整说明

## 当前参数设置

### 速度参数
```c
#define ZX_SPEED        15   // 直行速度（增加到15）
#define MINI_GNGLE      2    // 小角度差速
#define BIG_GNGLE       4    // 大角度差速
#define RIGHT_GNGLE     23   // 直角转弯差速（增强转弯力度）
```

## 7路传感器说明

### 传感器布局
```
D1  D2  D3  D4  D5  D6  D7
左  左  左  中  右  右  右
外  中  内  心  内  中  外
```

### 传感器引脚分配
- D1: GPIO_HWCGQ_PIN_0_PIN
- D2: GPIO_HWCGQ_PIN_1_PIN  
- D3: GPIO_HWCGQ_PIN_2_PIN
- D4: GPIO_HWCGQ_PIN_3_PIN
- D5: GPIO_HWCGQ_PIN_4_PIN
- D6: GPIO_HWCGQ_PIN_5_PIN (PB5)
- D7: GPIO_HWCGQ_PIN_6_PIN (PB10)

## 7路传感器循迹逻辑

### 转弯逻辑（7路传感器版）
```c
// 1. 左转直角 - 最左侧传感器检测到黑线
if(D1==1 || D2==1)
{
    // 左转 - 左轮慢，右轮快
    aim_speed1 = ZX_SPEED - RIGHT_GNGLE;
    aim_speed2 = ZX_SPEED + RIGHT_GNGLE;
}

// 2. 右转直角 - 最右侧传感器检测到黑线
else if(D6==1 || D7==1)
{
    // 右转 - 左轮快，右轮慢
    aim_speed1 = ZX_SPEED + RIGHT_GNGLE;
    aim_speed2 = ZX_SPEED - RIGHT_GNGLE;
}

// 3. 中间传感器检测到黑线 - 直行
else if(D4==1)
{
    aim_speed1 = ZX_SPEED;
    aim_speed2 = ZX_SPEED;
}

// 4. 轻微左偏 - 左中传感器检测到
else if(D3==1)
{
    aim_speed1 = ZX_SPEED - MINI_GNGLE;
    aim_speed2 = ZX_SPEED + MINI_GNGLE;
}

// 5. 轻微右偏 - 右中传感器检测到
else if(D5==1)
{
    aim_speed1 = ZX_SPEED + MINI_GNGLE;
    aim_speed2 = ZX_SPEED - MINI_GNGLE;
}

// 6. 严重左偏 - 左外侧传感器检测到
else if(D2==1)
{
    aim_speed1 = ZX_SPEED - BIG_GNGLE;
    aim_speed2 = ZX_SPEED + BIG_GNGLE;
}

// 7. 严重右偏 - 右外侧传感器检测到
else if(D6==1)
{
    aim_speed1 = ZX_SPEED + BIG_GNGLE;
    aim_speed2 = ZX_SPEED - BIG_GNGLE;
}

// 8. 完全偏离黑线的情况 - 保持当前方向
else
{
    aim_speed1 = ZX_SPEED;
    aim_speed2 = ZX_SPEED;
}
```

## 参数调整原因

#### 1. 增加直行速度
- **原因**：提高整体运行效率
- **效果**：小车运行更流畅，减少停顿

#### 2. 增强转弯力度
- **原因**：确保能够完成直角转弯
- **效果**：
  - `RIGHT_GNGLE`设置为23：增强转弯力度
  - 适合高速运行时的转弯需求

#### 3. 7路传感器优势
- **原因**：提供更精确的循迹控制
- **效果**：
  - 更精确的偏离检测
  - 更平滑的转弯控制
  - 更好的循迹精度

## 优化效果

### 解决的问题
1. **转弯响应慢**：移除复杂判定，直接响应传感器
2. **逻辑复杂**：简化代码，提高可读性
3. **适合矩形运动**：专门针对矩形框运动优化
4. **精度提升**：7路传感器提供更精确的控制

### 简化功能
1. **直接转弯**：检测到传感器立即转弯
2. **无计时器**：移除转弯时间控制
3. **无修正阶段**：简化逻辑流程
4. **7路控制**：更精确的传感器控制

### 预期效果
- 转弯响应更快
- 代码更简洁
- 适合矩形框运动
- 提高运行速度
- 更精确的循迹控制

## 进一步调整建议

### 如果转弯力度不够
```c
#define RIGHT_GNGLE     25   // 增加直角转弯差速
```

### 如果转弯过度
```c
#define RIGHT_GNGLE     20   // 减少直角转弯差速
```

### 如果直行速度太慢
```c
#define ZX_SPEED        18   // 增加直行速度
```

### 如果直行速度太快
```c
#define ZX_SPEED        12   // 减少直行速度
```

## 测试建议

### 1. 直角转弯测试
- 观察转弯是否及时响应
- 检查转弯角度是否合适
- 确认转弯力度是否足够

### 2. 直行测试
- 观察直行是否稳定
- 检查是否有左右摇摆
- 确认速度是否合适

### 3. 整体测试
- 观察整体运行效果
- 检查矩形框运动是否流畅
- 确认循迹精度

## 7路传感器控制逻辑说明

### 转弯触发条件
```c
// 左转直角
D1==1 || D2==1

// 右转直角
D6==1 || D7==1

// 直行
D4==1

// 轻微左偏
D3==1

// 轻微右偏
D5==1

// 严重左偏
D2==1

// 严重右偏
D6==1
```

### 转弯执行过程
1. **检测传感器**：实时检测7路传感器状态
2. **直接响应**：立即执行相应的转弯动作
3. **持续执行**：直到传感器状态改变

### 优势
- **响应快**：无延迟，立即响应
- **逻辑简单**：易于理解和调试
- **适合矩形**：专门针对矩形框运动
- **精度高**：7路传感器提供更精确控制

## 参数微调步骤

### 步骤1：基础测试
使用当前参数进行测试，观察转弯效果

### 步骤2：针对性调整
根据测试结果，针对性调整参数：
- 转弯不够 → 增加`RIGHT_GNGLE`
- 转弯过度 → 减少`RIGHT_GNGLE`
- 速度不合适 → 调整`ZX_SPEED`

### 步骤3：逐步优化
每次只调整一个参数，观察效果后再调整下一个

### 步骤4：最终确认
找到最佳参数组合后，进行多次测试确认

## 注意事项

1. **传感器灵敏度**：确保7路传感器工作正常
2. **地面条件**：不同地面摩擦力会影响转弯效果
3. **速度与转弯的关系**：速度越快，转弯力度可能需要相应调整
4. **渐进调整**：建议渐进式调整，避免参数变化过大
5. **传感器校准**：确保7路传感器都正确校准

## 最佳实践

1. **记录参数**：记录每次调整的参数和效果
2. **多次测试**：每个参数组合都要进行多次测试
3. **对比分析**：对比不同参数组合的效果
4. **稳定优先**：优先保证稳定性，再考虑速度
5. **简单有效**：保持逻辑简单，确保效果
6. **传感器维护**：定期检查和校准7路传感器

通过以上7路传感器的优化，应该能够实现更精确的循迹控制和快速响应的直角转弯，适合矩形框运动。 