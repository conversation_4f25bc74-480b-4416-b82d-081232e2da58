# 优化转弯方案使用说明

## 概述
本次优化主要针对七路传感器的转弯逻辑，特别是当左边或右边两位传感器识别到黑线时的转弯处理。优化后的方案提供了更智能、更精确的转弯控制。

## 优化内容

### 1. 新增转弯参数常量
在 `Trailing.h` 中新增了以下常量：
```c
#define AGGRESSIVE_TURN_SPEED  35    // 激进转弯时的速度
#define REVERSE_TURN_SPEED     25    // 反转转弯时的速度
#define MEDIUM_TURN_SPEED      25    // 中等转弯时的速度
#define LIGHT_REVERSE_SPEED    15    // 轻微反转速度
```

### 2. 优化后的转弯逻辑

#### 2.1 左边两位传感器检测到黑线时的转弯处理
- **D1和D2同时检测到**：激进左转
  - 左轮反转：-25
  - 右轮全速：35
  
- **D2和D3同时检测到**：中等左转
  - 左轮反转：-15
  - 右轮加速：25
  
- **D1和D3同时检测到**：激进左转
  - 左轮反转：-25
  - 右轮全速：35

#### 2.2 右边两位传感器检测到黑线时的转弯处理
- **D6和D7同时检测到**：激进右转
  - 左轮全速：35
  - 右轮反转：-25
  
- **D5和D6同时检测到**：中等右转
  - 左轮加速：25
  - 右轮反转：-15
  
- **D5和D7同时检测到**：激进右转
  - 左轮全速：35
  - 右轮反转：-25

### 3. 新增优化转弯函数

#### 3.1 `optimized_turn_trailing()` 函数
这是一个专门针对两位传感器转弯逻辑优化的函数，具有以下特点：
- 更简洁的逻辑结构
- 更精确的转弯控制
- 更好的响应速度

#### 3.2 使用方法
在 `main.c` 中，可以将：
```c
while (1) {
    Veer_Trailing();
}
```
替换为：
```c
while (1) {
    optimized_turn_trailing();
}
```

## 转弯策略说明

### 1. 激进转弯
当最外侧两位传感器（D1+D2 或 D6+D7）检测到黑线时，采用激进转弯策略：
- 一侧轮子反转，另一侧轮子全速前进
- 转弯半径小，适合急转弯

### 2. 中等转弯
当中间两位传感器（D2+D3 或 D5+D6）检测到黑线时，采用中等转弯策略：
- 一侧轮子轻微反转，另一侧轮子加速
- 转弯半径适中，适合一般转弯

### 3. 轻微修正
当单个传感器检测到黑线时，采用轻微修正策略：
- 通过差速进行轻微方向调整
- 保持平稳行驶

## 参数调整建议

### 1. 转弯速度调整
- 如果转弯过快，可以降低 `AGGRESSIVE_TURN_SPEED` 的值
- 如果转弯过慢，可以增加 `REVERSE_TURN_SPEED` 的值

### 2. 转弯灵敏度调整
- 如果转弯过于激进，可以增加 `LIGHT_REVERSE_SPEED` 的值
- 如果转弯不够灵敏，可以降低 `MEDIUM_TURN_SPEED` 的值

## 测试建议

### 1. 基础测试
1. 测试直线行驶：确保中间传感器（D4）检测到黑线时能直行
2. 测试轻微偏离：测试单个传感器检测时的修正效果
3. 测试两位传感器：重点测试左边或右边两位传感器的转弯效果

### 2. 转弯测试
1. 测试左转：使用左边两位传感器检测的场景
2. 测试右转：使用右边两位传感器检测的场景
3. 测试急转弯：使用最外侧两位传感器的场景

### 3. 复杂场景测试
1. 测试十字路口：所有传感器都检测到黑线的情况
2. 测试偏离恢复：从完全偏离状态恢复到正常循迹
3. 测试连续转弯：连续的左右转弯场景

## 注意事项

1. **电机方向**：确保左右电机的正反转方向设置正确
2. **传感器灵敏度**：根据实际环境调整传感器的触发阈值
3. **转弯速度**：根据赛道宽度和转弯半径调整转弯参数
4. **电池电量**：确保电池电量充足，避免转弯时动力不足

## 故障排除

### 1. 转弯过度
- 降低 `AGGRESSIVE_TURN_SPEED` 的值
- 增加 `LIGHT_REVERSE_SPEED` 的值

### 2. 转弯不足
- 增加 `REVERSE_TURN_SPEED` 的值
- 降低 `MEDIUM_TURN_SPEED` 的值

### 3. 转弯不稳定
- 检查传感器安装是否牢固
- 检查电机连接是否正常
- 调整PID参数

通过以上优化，小车的转弯性能将得到显著提升，特别是在处理复杂转弯场景时更加稳定和精确。 