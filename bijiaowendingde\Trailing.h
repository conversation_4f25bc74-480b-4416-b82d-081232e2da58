#ifndef __TRAILING_H
#define __TRAILING_H	

#include "ti_msp_dl_config.h"

// 基本巡线参数设置
#define ZX_SPEED			 10	// 直行的速度（增加基础速度）
#define MINI_GNGLE			 4	// 小角度偏离回正的差速
#define BIG_GNGLE			 8	// 大角度偏离回正的差速
#define RIGHT_GNGLE		     30	// 直角转弯时的差速（进一步增大）

// 新增：优化转弯参数（降低激进程度，减少甩尾）
#define AGGRESSIVE_TURN_SPEED  28	// 激进转弯时的速度（从35降到28）
#define REVERSE_TURN_SPEED     20	// 反转转弯时的速度（从25降到20）
#define MEDIUM_TURN_SPEED      20	// 中等转弯时的速度（从25降到20）
#define LIGHT_REVERSE_SPEED    12	// 轻微反转速度（从15降到12）

// 新增：转弯稳定性参数
#define SENSOR_DEBOUNCE_COUNT  4     // 传感器防抖计数（从3增加到4）
#define TURN_CONSISTENCY_TIME  50    // 转弯一致性时间(ms)
#define SPEED_SMOOTH_FACTOR    0.85f // 速度平滑因子（从0.8f增加到0.85f）

extern int D1,D2,D3,D4,D5,D6,D7;
extern int16_t aim_speed1;     
extern int16_t aim_speed2;  

// 基本函数声明
void Trailing_detection(void);
void Veer_Trailing(void);
void test_motors(void);  // 添加电机测试函数
void simple_trailing_test(void);  // 添加简单循迹测试函数
void right_angle_test(void);  // 添加直角转弯测试函数
void sensor_debug(void);  // 添加传感器调试函数
void optimized_turn_trailing(void);  // 添加优化转弯函数
void stable_turn_trailing(void);  // 添加稳定转弯函数
void anti_fishtail_turn_trailing(void);  // 添加防甩尾转弯函数

#endif  
