# 直角转弯问题排查指南

## 问题现象
小车到达直角转弯时没有转弯，继续直行或冲出黑线。

## 可能原因分析

### 1. 传感器检测问题
**现象**：传感器没有正确检测到直角转弯的传感器组合
**排查方法**：
- 检查传感器连接是否正确
- 观察传感器在直角转弯时的状态
- 确认传感器检测逻辑是否正确

### 2. 条件优先级问题
**现象**：其他条件先被触发，直角转弯条件没有被执行
**解决方案**：已调整代码，将直角转弯检测放在最高优先级

### 3. 转弯力度不够
**现象**：检测到直角转弯但转弯力度太小
**解决方案**：已将`RIGHT_GNGLE`从4增加到6

### 4. 传感器组合不匹配
**现象**：实际的传感器组合与代码中的条件不匹配
**排查方法**：观察实际的传感器状态

## 调试步骤

### 步骤1：观察传感器状态
在直角转弯时，观察5个传感器的状态：
```
D1  D2  D3  D4  D5
```

### 步骤2：检查代码中的条件
当前代码中的直角转弯条件：

#### 左转直角条件：
```c
(D1==1 && D2==1 && D3==1 && D4==0 && D5==0) ||
(D1==1 && D2==1 && D3==0 && D4==0 && D5==0) ||
(D1==1 && D2==0 && D3==1 && D4==0 && D5==0)
```

#### 右转直角条件：
```c
(D1==0 && D2==0 && D3==1 && D4==1 && D5==1) ||
(D1==0 && D2==0 && D3==0 && D4==1 && D5==1) ||
(D1==0 && D2==0 && D3==1 && D4==0 && D5==1)
```

### 步骤3：添加调试代码
可以在`Veer_Trailing`函数中添加调试信息：

```c
// 在直角转弯检测后添加
if((D1==1 && D2==1 && D3==1 && D4==0 && D5==0) ||
   (D1==1 && D2==1 && D3==0 && D4==0 && D5==0) ||
   (D1==1 && D2==0 && D3==1 && D4==0 && D5==0))
{
    // 左转直角
    aim_speed1 = ZX_SPEED - RIGHT_GNGLE;
    aim_speed2 = ZX_SPEED + RIGHT_GNGLE;
    
    // 调试信息
    // 这里可以添加LED指示或串口输出
}
```

## 常见问题及解决方案

### 问题1：传感器状态不匹配
**现象**：实际传感器状态与代码条件不符
**解决**：根据实际观察到的传感器状态修改代码条件

### 问题2：转弯力度仍然不够
**现象**：检测到直角转弯但转弯角度不够
**解决**：进一步增加`RIGHT_GNGLE`的值
```c
#define RIGHT_GNGLE     8    // 从6增加到8
```

### 问题3：转弯过度
**现象**：转弯角度过大，冲出黑线
**解决**：减少`RIGHT_GNGLE`的值
```c
#define RIGHT_GNGLE     4    // 从6减少到4
```

### 问题4：传感器检测不稳定
**现象**：传感器状态频繁变化
**解决**：
- 检查传感器连接
- 调整传感器高度
- 增加滤波处理

## 测试建议

### 1. 静态测试
将小车放在直角转弯位置，观察传感器状态：
- 左转直角：`D1=1, D2=1, D3=1, D4=0, D5=0`
- 右转直角：`D1=0, D2=0, D3=1, D4=1, D5=1`

### 2. 动态测试
让小车缓慢接近直角转弯，观察：
- 传感器状态变化
- 电机速度变化
- 转弯效果

### 3. 参数调整测试
逐步调整`RIGHT_GNGLE`参数：
- 从4开始测试
- 如果不够，增加到6
- 如果过度，减少到3

## 代码优化建议

如果问题仍然存在，可以考虑以下优化：

### 1. 增加更多传感器组合
```c
// 增加更多左转直角条件
(D1==1 && D2==1 && D3==1 && D4==0 && D5==0) ||
(D1==1 && D2==1 && D3==0 && D4==0 && D5==0) ||
(D1==1 && D2==0 && D3==1 && D4==0 && D5==0) ||
(D1==1 && D2==0 && D3==0 && D4==0 && D5==0)  // 新增
```

### 2. 增加转弯时间控制
```c
// 在直角转弯时增加持续时间
static uint8_t turn_timer = 0;
if(检测到直角转弯) {
    turn_timer = 10;  // 保持转弯状态10个周期
}
if(turn_timer > 0) {
    turn_timer--;
    // 继续转弯
}
```

### 3. 增加速度控制
```c
// 在直角转弯时降低整体速度
if(检测到直角转弯) {
    aim_speed1 = (ZX_SPEED - RIGHT_GNGLE) * 0.8;  // 降低速度
    aim_speed2 = (ZX_SPEED + RIGHT_GNGLE) * 0.8;
}
```

## 总结
直角转弯不工作通常是由于传感器检测、条件优先级或转弯力度问题导致的。通过以上排查步骤，应该能够找到并解决问题。 