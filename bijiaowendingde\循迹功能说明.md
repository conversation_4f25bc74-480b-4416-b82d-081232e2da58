# 七路传感器循迹功能说明

## 功能特点

### 1. 基本循迹
- 使用7路红外传感器进行黑线检测
- 支持直线、曲线、直角转弯等多种路径
- 差速转向控制，响应灵敏

### 2. 激进直角转弯策略
- **最外侧传感器检测**：内侧轮反转，外侧轮全速
- **复合传感器检测**：多个同侧传感器检测到时，采用反转+全速策略
- **最大转弯力矩**：通过轮子反转提供最大的转弯效果

## 参数设置

```c
#define ZX_SPEED        10   // 基础速度
#define MINI_GNGLE      4    // 小角度修正
#define BIG_GNGLE       8    // 大角度修正
#define RIGHT_GNGLE     30   // 直角转弯修正（已优化）
```

## 使用方法

### 1. 编译下载
```bash
cd Debug
make clean
make
```

### 2. 测试准备
- 准备黑线路径（建议宽度2-3cm）
- 确保传感器安装高度合适（距离地面5-10mm）
- 检查电池电量充足

### 3. 循迹测试
1. 将小车放在黑线起点
2. 确保中间传感器对准黑线
3. 启动程序
4. 观察循迹效果

## 循迹逻辑

### 1. 直线循迹
- 中间传感器检测到黑线时直行
- 轻微偏离时进行小角度修正

### 2. 曲线循迹
- 根据偏离程度进行差速控制
- 支持各种角度的曲线路径

### 3. 激进直角转弯
- **最外侧传感器检测**：内侧轮反转(-15)，外侧轮全速(25)
- **多个同侧传感器检测**：内侧轮反转(-20)，外侧轮全速(30)
- **反转策略**：通过轮子反转提供最大的转弯力矩

## 直角转弯策略详解

### 单传感器直角转弯
```c
// 左直角转弯（D1检测到）
aim_speed1 = -15;  // 左轮反转
aim_speed2 = 25;   // 右轮全速

// 右直角转弯（D7检测到）
aim_speed1 = 25;   // 左轮全速
aim_speed2 = -15;  // 右轮反转
```

### 复合传感器直角转弯
```c
// 多个左侧传感器检测到
aim_speed1 = -20;  // 左轮反转
aim_speed2 = 30;   // 右轮全速

// 多个右侧传感器检测到
aim_speed1 = 30;   // 左轮全速
aim_speed2 = -20;  // 右轮反转
```

## 常见问题解决

### 1. 直角转弯不转
- 检查传感器是否正常工作
- 验证黑线是否足够明显
- 调整传感器安装高度

### 2. 转弯太急或太慢
- 调整反转和全速的速度值
- 检查复合传感器检测逻辑

### 3. 循迹不准确
- 清洁传感器表面
- 调整传感器安装高度
- 检查黑线质量

## 参数调整建议

### 如果直角转弯太慢
```c
// 增大反转和全速的速度值
aim_speed1 = -25;  // 增大反转速度
aim_speed2 = 35;   // 增大全速速度
```

### 如果直角转弯太急
```c
// 减小反转和全速的速度值
aim_speed1 = -10;  // 减小反转速度
aim_speed2 = 20;   // 减小全速速度
```

### 如果基础速度不合适
```c
#define ZX_SPEED        8    // 减小基础速度
#define ZX_SPEED        12   // 增大基础速度
```

## 注意事项

1. **传感器维护**：定期清洁传感器表面
2. **路径质量**：黑线要清晰，背景要干净
3. **电池维护**：保持电池电量充足
4. **机械检查**：确保轮子转动顺畅
5. **反转测试**：确保电机支持反转功能

## 技术支持

如果遇到问题，请提供：
- 具体的现象描述
- 传感器检测状态
- 小车运动表现
- 环境条件 