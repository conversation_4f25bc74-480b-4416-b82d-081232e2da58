# 循迹调试指南

## 当前状态
- ✅ 轮子方向已修复，现在一致
- ✅ 小车可以直行
- ⚠️ 速度略有差异
- ⚠️ 需要测试循迹功能

## 调试步骤

### 1. 测试简单循迹功能
1. 将 `test_mode` 设置为 2
2. 编译并下载程序
3. 将小车放在黑线上（中间传感器对准黑线）
4. 观察小车是否前进
5. 将小车移开黑线，观察是否停止

### 2. 测试完整循迹功能
1. 将 `test_mode` 设置为 0
2. 编译并下载程序
3. 将小车放在黑线路径上
4. 观察循迹效果

### 3. 解决速度差异问题

如果两个轮子速度不一致，可以尝试以下方法：

#### 方法1：调整PID参数
```c
// 如果左轮比右轮快，减小左轮的kp
MOTOR_A.param.kp = 160;  // 减小左轮比例系数
MOTOR_B.param.kp = 180;  // 保持右轮不变

// 如果右轮比左轮快，减小右轮的kp
MOTOR_A.param.kp = 180;  // 保持左轮不变
MOTOR_B.param.kp = 160;  // 减小右轮比例系数
```

#### 方法2：检查编码器
- 确保编码器接线正确
- 检查编码器是否正常工作
- 观察编码器计数是否准确

#### 方法3：硬件调整
- 检查电机安装是否对称
- 检查轮子是否磨损一致
- 检查电池电压是否稳定

## 测试模式说明

### 模式0：完整循迹 (test_mode = 0)
- 使用完整的七路传感器循迹算法
- 支持各种偏离情况的修正
- 适合正式循迹测试

### 模式1：电机测试 (test_mode = 1)
- 两个轮子都以相同速度前进
- 用于验证电机方向是否正确
- 不涉及传感器

### 模式2：简单循迹测试 (test_mode = 2)
- 只有中间传感器工作
- 检测到黑线就前进，否则停止
- 用于验证传感器是否正常工作

## 常见问题排查

### 1. 小车不循迹
- 检查传感器是否正常工作
- 检查传感器安装高度
- 验证黑线是否足够明显

### 2. 循迹不准确
- 调整循迹参数
- 检查传感器排列是否对称
- 优化循迹算法

### 3. 速度不稳定
- 调整PID参数
- 检查编码器反馈
- 确保电源稳定

## 参数调整建议

### 如果循迹太敏感
```c
#define MINI_GNGLE      2    // 减小小角度修正
#define BIG_GNGLE       4    // 减小大角度修正
#define RIGHT_GNGLE     8    // 减小直角转弯修正
```

### 如果循迹太迟钝
```c
#define MINI_GNGLE      4    // 增大小角度修正
#define BIG_GNGLE       8    // 增大大角度修正
#define RIGHT_GNGLE     16   // 增大直角转弯修正
```

### 如果速度太慢
```c
#define ZX_SPEED        10   // 增加基础速度
```

### 如果速度太快
```c
#define ZX_SPEED        6    // 减小基础速度
``` 