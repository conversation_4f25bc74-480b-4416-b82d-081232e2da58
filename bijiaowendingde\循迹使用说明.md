# 七路传感器循迹使用说明

## 当前状态
- ✅ 轮子方向已修复
- ✅ 电机测试功能正常
- ✅ 循迹算法已优化
- ✅ 参数已调整

## 使用方法

### 1. 直接使用循迹功能
当前程序已设置为循迹模式（`test_mode = 0`），直接编译下载即可使用。

### 2. 测试模式说明
如果需要测试其他功能，可以修改 `main.c` 中的 `test_mode`：

```c
uint8_t test_mode = 0; // 0:正常循迹, 1:电机测试, 2:简单循迹测试
```

- **模式0**：完整循迹（推荐使用）
- **模式1**：电机测试（验证电机方向）
- **模式2**：简单循迹测试（仅中间传感器）

## 循迹算法特点

### 1. 优化的检测逻辑
- **单传感器检测**：精确的偏离修正
- **复合传感器检测**：根据检测数量动态调整
- **十字路口处理**：保持直行方向

### 2. 差速控制策略
- **轻微偏离**：差速4（MINI_GNGLE）
- **中等偏离**：差速8（BIG_GNGLE）
- **严重偏离**：差速15（RIGHT_GNGLE）
- **复合偏离**：根据传感器数量动态计算

### 3. 参数设置
```c
#define ZX_SPEED        10   // 基础速度
#define MINI_GNGLE      4    // 小角度修正
#define BIG_GNGLE       8    // 大角度修正
#define RIGHT_GNGLE     15   // 直角转弯修正
```

## 使用步骤

### 1. 编译下载
```bash
cd Debug
make clean
make
```

### 2. 测试准备
- 准备黑线路径（建议宽度2-3cm）
- 确保传感器安装高度合适（距离地面5-10mm）
- 检查电池电量充足

### 3. 循迹测试
1. 将小车放在黑线起点
2. 确保中间传感器对准黑线
3. 启动程序
4. 观察循迹效果

## 常见问题解决

### 1. 小车不循迹
- 检查传感器是否正常工作
- 验证黑线是否足够明显
- 调整传感器安装高度

### 2. 循迹不准确
- 如果转向太急：减小差速参数
- 如果转向太慢：增大差速参数
- 如果速度太慢：增大ZX_SPEED

### 3. 速度不稳定
- 检查PID参数设置
- 验证编码器工作正常
- 确保电源稳定

## 参数调整建议

### 如果循迹太敏感（容易震荡）
```c
#define MINI_GNGLE      2    // 减小小角度修正
#define BIG_GNGLE       6    // 减小大角度修正
#define RIGHT_GNGLE     12   // 减小直角转弯修正
```

### 如果循迹太迟钝（转向慢）
```c
#define MINI_GNGLE      6    // 增大小角度修正
#define BIG_GNGLE       10   // 增大大角度修正
#define RIGHT_GNGLE     18   // 增大直角转弯修正
```

### 如果速度不合适
```c
#define ZX_SPEED        8    // 减小基础速度
#define ZX_SPEED        12   // 增大基础速度
```

## 调试技巧

### 1. 观察传感器状态
可以通过调试器观察 `D1-D7` 变量的值，了解传感器检测情况。

### 2. 观察速度设置
观察 `aim_speed1` 和 `aim_speed2` 的值，了解差速控制效果。

### 3. 逐步调整
建议从当前参数开始，根据实际效果逐步微调，每次只调整一个参数。

## 注意事项

1. **传感器清洁**：定期清洁传感器表面，确保检测准确
2. **路径质量**：黑线要清晰，背景要干净
3. **电池维护**：保持电池电量充足，电压稳定
4. **机械检查**：确保轮子转动顺畅，无卡顿

## 技术支持

如果遇到问题，请提供以下信息：
- 具体的现象描述
- 传感器检测状态
- 小车运动表现
- 环境条件（光线、地面等） 