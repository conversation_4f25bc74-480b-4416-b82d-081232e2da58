# 电机调试说明

## 问题分析

### 1. 轮子转向不一致的原因
- 电机驱动器的方向控制信号配置错误
- 左右轮的方向控制逻辑不一致
- 电机接线可能接反

### 2. 速度不稳定的原因
- PID参数设置过于激进
- 编码器反馈有问题
- 循迹参数设置不当

## 解决方案

### 1. 修复电机方向控制
已修复 `tb6612_moto.c` 中的方向控制逻辑：
```c
// 修复前：左右轮方向控制不一致
// 修复后：统一方向控制逻辑
```

### 2. 调整PID参数
```c
// 修复前：过于激进
.kp = 300, .ki = 50, .kd = 0.0f

// 修复后：更稳定
.kp = 200, .ki = 30, .kd = 5.0f
```

### 3. 优化循迹参数
```c
// 修复前：速度较慢，转向不够
#define ZX_SPEED        5
#define MINI_GNGLE      2
#define BIG_GNGLE       4
#define RIGHT_GNGLE     8

// 修复后：速度更快，转向更明显
#define ZX_SPEED        8
#define MINI_GNGLE      3
#define BIG_GNGLE       6
#define RIGHT_GNGLE     12
```

## 调试步骤

### 1. 电机方向测试
1. 将 `test_mode` 设置为 1
2. 编译并下载程序
3. 观察小车是否直线前进
4. 如果转向错误，检查电机接线

### 2. 循迹功能测试
1. 将 `test_mode` 设置为 0
2. 将小车放在黑线路径上
3. 观察循迹效果
4. 根据效果调整参数

### 3. 参数微调
如果循迹效果不理想，可以调整以下参数：

#### PID参数调整
```c
// 如果速度震荡，减小 kp 和 ki
.kp = 150, .ki = 20, .kd = 5.0f

// 如果响应太慢，增大 kp
.kp = 250, .ki = 30, .kd = 5.0f
```

#### 循迹参数调整
```c
// 如果转向太急，减小差速
#define MINI_GNGLE      2
#define BIG_GNGLE       4
#define RIGHT_GNGLE     8

// 如果转向太慢，增大差速
#define MINI_GNGLE      4
#define BIG_GNGLE       8
#define RIGHT_GNGLE     16
```

## 常见问题排查

### 1. 小车原地打转
- 检查电机方向是否正确
- 检查编码器接线
- 调整PID参数

### 2. 小车走直线但转向不准确
- 调整循迹参数
- 检查传感器安装高度
- 优化循迹算法

### 3. 速度不稳定
- 调整PID参数
- 检查编码器
- 检查电机驱动器

## 测试模式说明

### 电机测试模式 (test_mode = 1)
- 两个轮子都以相同速度前进
- 用于验证电机方向是否正确
- 如果小车不直线前进，说明电机方向有问题

### 循迹模式 (test_mode = 0)
- 正常的七路传感器循迹功能
- 根据传感器状态调整轮子速度
- 实现自动路径跟踪 