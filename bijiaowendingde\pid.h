 /******************** (C) COPYRIGHT 2025   B站：电子大师兄 ****************************
* 文件名    : pid.h
* 作者      : 电子大师兄（https://space.bilibili.com/568487373）
* 版本      : V1.0
* 时间      : 2025-7-23
* 描述      : PID模块
*******************************************************************************/
#ifndef __PID_H__
#define __PID_H__

#include "ti_msp_dl_config.h"

typedef struct {
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float outMax;      // 输出上限
    float outMin;      // 输出下限
} PID_Param_t;

typedef struct {
    PID_Param_t param; // PID 参数
    float integral;    // 积分累计
    float lastError;   // 上一次误差
    float output;      // 本次计算结果
    int   ErrorAngle;   
} PID_t;

extern PID_t MOTOR_A; 
extern PID_t MOTOR_B; 

float PID_Calc(PID_t *pid, float setpoint, float feedback);

#endif
