<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o M0G3507_PID_N20.out -mM0G3507_PID_N20.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/XiangMu/workspace_CCS/M0G3507_PID_N20 -iC:/Users/<USER>/XiangMu/workspace_CCS/M0G3507_PID_N20/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=M0G3507_PID_N20_linkInfo.xml --rom_model ./encoder.o ./main.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./pid.o ./tb6612_moto.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x6881daec</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\M0G3507_PID_N20.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x8ad</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\.\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\.\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\.\</path>
         <kind>object</kind>
         <file>tb6612_moto.o</file>
         <name>tb6612_moto.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\XiangMu\workspace_CCS\M0G3507_PID_N20\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text.Pid_Speed</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0xfc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text</name>
         <load_address>0x2c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c0</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text:memcpy</name>
         <load_address>0x398</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x398</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x432</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x432</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.__mulsf3</name>
         <load_address>0x434</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x434</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x4c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4c0</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.main</name>
         <load_address>0x53c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x53c</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.SYSCFG_DL_PWMA_init</name>
         <load_address>0x5b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b4</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.calc_encoder_cnt</name>
         <load_address>0x620</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x620</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x680</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x680</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.n20_moto</name>
         <load_address>0x6dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6dc</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.__floatsisf</name>
         <load_address>0x72c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x72c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x768</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x768</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.__muldsi3</name>
         <load_address>0x7a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a4</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.__fixsfsi</name>
         <load_address>0x7e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x818</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x818</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x84c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x84c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x87c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x87c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x8ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8ac</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x8d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8d4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x8f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8f0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x90c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x90c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x924</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x924</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.calc_speed</name>
         <load_address>0x93c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x93c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text.init_encoder</name>
         <load_address>0x954</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x954</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x968</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x968</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x97c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x97c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.init_moto</name>
         <load_address>0x98c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x98c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x99c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x99c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x9a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9a6</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x9ae</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9ae</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x9b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9b8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text:abort</name>
         <load_address>0x9c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c0</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.HOSTexit</name>
         <load_address>0x9c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x9ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9ca</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text._system_pre_init</name>
         <load_address>0x9ce</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9ce</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.cinit..data.load</name>
         <load_address>0x9e8</load_address>
         <readonly>true</readonly>
         <run_address>0x9e8</run_address>
         <size>0x15</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-15d">
         <name>__TI_handler_table</name>
         <load_address>0xa00</load_address>
         <readonly>true</readonly>
         <run_address>0xa00</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-15e">
         <name>__TI_cinit_table</name>
         <load_address>0xa08</load_address>
         <readonly>true</readonly>
         <run_address>0xa08</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-107">
         <name>.rodata.gPWMAConfig</name>
         <load_address>0x9d8</load_address>
         <readonly>true</readonly>
         <run_address>0x9d8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-106">
         <name>.rodata.gPWMAClockConfig</name>
         <load_address>0x9e0</load_address>
         <readonly>true</readonly>
         <run_address>0x9e0</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-127">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-7d">
         <name>.data.encoder_cnt</name>
         <load_address>0x2020001c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020001c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.data.cur_speed</name>
         <load_address>0x20200018</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200018</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.data.aim_speed</name>
         <load_address>0x20200014</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200014</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.data.kp</name>
         <load_address>0x2020002c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020002c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.data.ki</name>
         <load_address>0x20200028</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200028</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.data.kd</name>
         <load_address>0x20200024</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200024</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.data.Pid_Speed.last_speed</name>
         <load_address>0x2020000c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020000c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.data.Pid_Speed.last_err.0</name>
         <load_address>0x20200004</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.data.Pid_Speed.last_err.1</name>
         <load_address>0x20200008</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200008</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.data.Pid_Speed.remainder</name>
         <load_address>0x20200010</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200010</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.data.Pid_Speed.cal_pwm</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.data.g_dir</name>
         <load_address>0x20200030</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200030</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.data.g_pwm</name>
         <load_address>0x20200020</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200020</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-161">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_loc</name>
         <load_address>0x7d</load_address>
         <run_address>0x7d</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_loc</name>
         <load_address>0xbe</load_address>
         <run_address>0xbe</run_address>
         <size>0xc5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_loc</name>
         <load_address>0x183</load_address>
         <run_address>0x183</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_loc</name>
         <load_address>0x1d3</load_address>
         <run_address>0x1d3</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_loc</name>
         <load_address>0x1e6</load_address>
         <run_address>0x1e6</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x1c0d</load_address>
         <run_address>0x1c0d</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_loc</name>
         <load_address>0x1ce5</load_address>
         <run_address>0x1ce5</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x2109</load_address>
         <run_address>0x2109</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x2178</load_address>
         <run_address>0x2178</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_loc</name>
         <load_address>0x22df</load_address>
         <run_address>0x22df</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x17f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0x17f</load_address>
         <run_address>0x17f</run_address>
         <size>0x7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_abbrev</name>
         <load_address>0x1fc</load_address>
         <run_address>0x1fc</run_address>
         <size>0x1ee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x3ea</load_address>
         <run_address>0x3ea</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_abbrev</name>
         <load_address>0x457</load_address>
         <run_address>0x457</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x4fb</load_address>
         <run_address>0x4fb</run_address>
         <size>0x129</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x624</load_address>
         <run_address>0x624</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_abbrev</name>
         <load_address>0x686</load_address>
         <run_address>0x686</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x90c</load_address>
         <run_address>0x90c</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_abbrev</name>
         <load_address>0x9bb</load_address>
         <run_address>0x9bb</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_abbrev</name>
         <load_address>0xb2b</load_address>
         <run_address>0xb2b</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xb64</load_address>
         <run_address>0xb64</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xbd4</load_address>
         <run_address>0xbd4</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_abbrev</name>
         <load_address>0xc61</load_address>
         <run_address>0xc61</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_abbrev</name>
         <load_address>0xcf9</load_address>
         <run_address>0xcf9</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_abbrev</name>
         <load_address>0xd25</load_address>
         <run_address>0xd25</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_abbrev</name>
         <load_address>0xd4c</load_address>
         <run_address>0xd4c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_abbrev</name>
         <load_address>0xd73</load_address>
         <run_address>0xd73</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_abbrev</name>
         <load_address>0xd9a</load_address>
         <run_address>0xd9a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0xdc1</load_address>
         <run_address>0xdc1</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_abbrev</name>
         <load_address>0xde8</load_address>
         <run_address>0xde8</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_abbrev</name>
         <load_address>0xe0f</load_address>
         <run_address>0xe0f</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_abbrev</name>
         <load_address>0xe34</load_address>
         <run_address>0xe34</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0xa6d</load_address>
         <run_address>0xa6d</run_address>
         <size>0x125</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0xb92</load_address>
         <run_address>0xb92</run_address>
         <size>0x202d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2bbf</load_address>
         <run_address>0x2bbf</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_info</name>
         <load_address>0x2c3f</load_address>
         <run_address>0x2c3f</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_info</name>
         <load_address>0x2dd8</load_address>
         <run_address>0x2dd8</run_address>
         <size>0xe6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0x3c43</load_address>
         <run_address>0x3c43</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_info</name>
         <load_address>0x3cb8</load_address>
         <run_address>0x3cb8</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x6e2a</load_address>
         <run_address>0x6e2a</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_info</name>
         <load_address>0x724d</load_address>
         <run_address>0x724d</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_info</name>
         <load_address>0x7991</load_address>
         <run_address>0x7991</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x79d7</load_address>
         <run_address>0x79d7</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x7a9d</load_address>
         <run_address>0x7a9d</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_info</name>
         <load_address>0x7c19</load_address>
         <run_address>0x7c19</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_info</name>
         <load_address>0x7d11</load_address>
         <run_address>0x7d11</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0x7d4c</load_address>
         <run_address>0x7d4c</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_info</name>
         <load_address>0x7ef3</load_address>
         <run_address>0x7ef3</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_info</name>
         <load_address>0x8080</load_address>
         <run_address>0x8080</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_info</name>
         <load_address>0x820f</load_address>
         <run_address>0x820f</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x83a2</load_address>
         <run_address>0x83a2</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_info</name>
         <load_address>0x853b</load_address>
         <run_address>0x853b</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_info</name>
         <load_address>0x86ca</load_address>
         <run_address>0x86ca</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_info</name>
         <load_address>0x89c4</load_address>
         <run_address>0x89c4</run_address>
         <size>0xae</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_ranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_ranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_ranges</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x280</load_address>
         <run_address>0x280</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_ranges</name>
         <load_address>0x2c8</load_address>
         <run_address>0x2c8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x310</load_address>
         <run_address>0x310</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_ranges</name>
         <load_address>0x360</load_address>
         <run_address>0x360</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_ranges</name>
         <load_address>0x378</load_address>
         <run_address>0x378</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x773</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_str</name>
         <load_address>0x773</load_address>
         <run_address>0x773</run_address>
         <size>0x16f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_str</name>
         <load_address>0x8e2</load_address>
         <run_address>0x8e2</run_address>
         <size>0x1a53</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x2335</load_address>
         <run_address>0x2335</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_str</name>
         <load_address>0x249a</load_address>
         <run_address>0x249a</run_address>
         <size>0x183</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_str</name>
         <load_address>0x261d</load_address>
         <run_address>0x261d</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x2d07</load_address>
         <run_address>0x2d07</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_str</name>
         <load_address>0x2e74</load_address>
         <run_address>0x2e74</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x4c40</load_address>
         <run_address>0x4c40</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_str</name>
         <load_address>0x4e65</load_address>
         <run_address>0x4e65</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_str</name>
         <load_address>0x5194</load_address>
         <run_address>0x5194</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x5289</load_address>
         <run_address>0x5289</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x53f1</load_address>
         <run_address>0x53f1</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_str</name>
         <load_address>0x55c6</load_address>
         <run_address>0x55c6</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_str</name>
         <load_address>0x570e</load_address>
         <run_address>0x570e</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_frame</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x128</load_address>
         <run_address>0x128</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_frame</name>
         <load_address>0x158</load_address>
         <run_address>0x158</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_frame</name>
         <load_address>0x188</load_address>
         <run_address>0x188</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_frame</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x5e8</load_address>
         <run_address>0x5e8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_frame</name>
         <load_address>0x678</load_address>
         <run_address>0x678</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0x778</load_address>
         <run_address>0x778</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x798</load_address>
         <run_address>0x798</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x7c0</load_address>
         <run_address>0x7c0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_frame</name>
         <load_address>0x7f0</load_address>
         <run_address>0x7f0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_frame</name>
         <load_address>0x820</load_address>
         <run_address>0x820</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x2c1</load_address>
         <run_address>0x2c1</run_address>
         <size>0x138</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_line</name>
         <load_address>0x3f9</load_address>
         <run_address>0x3f9</run_address>
         <size>0x4bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x8b5</load_address>
         <run_address>0x8b5</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_line</name>
         <load_address>0x96d</load_address>
         <run_address>0x96d</run_address>
         <size>0x176</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_line</name>
         <load_address>0xae3</load_address>
         <run_address>0xae3</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0xd27</load_address>
         <run_address>0xd27</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_line</name>
         <load_address>0xe9f</load_address>
         <run_address>0xe9f</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x260d</load_address>
         <run_address>0x260d</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0x27e9</load_address>
         <run_address>0x27e9</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0x2d03</load_address>
         <run_address>0x2d03</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x2d41</load_address>
         <run_address>0x2d41</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x2e01</load_address>
         <run_address>0x2e01</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_line</name>
         <load_address>0x2fc9</load_address>
         <run_address>0x2fc9</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_line</name>
         <load_address>0x3030</load_address>
         <run_address>0x3030</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_line</name>
         <load_address>0x3071</load_address>
         <run_address>0x3071</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_line</name>
         <load_address>0x3178</load_address>
         <run_address>0x3178</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_line</name>
         <load_address>0x3258</load_address>
         <run_address>0x3258</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_line</name>
         <load_address>0x3310</load_address>
         <run_address>0x3310</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x33cc</load_address>
         <run_address>0x33cc</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_line</name>
         <load_address>0x3470</load_address>
         <run_address>0x3470</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x3529</load_address>
         <run_address>0x3529</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x918</size>
         <contents>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x9e8</load_address>
         <run_address>0x9e8</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-15e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x9d8</load_address>
         <run_address>0x9d8</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-106"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-127"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x31</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-a2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-161"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11f" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-121" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-123" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-125" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-141" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2305</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-143" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe43</size>
         <contents>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-163"/>
         </contents>
      </logical_group>
      <logical_group id="lg-145" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8a72</size>
         <contents>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-162"/>
         </contents>
      </logical_group>
      <logical_group id="lg-147" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3a0</size>
         <contents>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-149" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x57f7</size>
         <contents>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-113"/>
         </contents>
      </logical_group>
      <logical_group id="lg-14b" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x840</size>
         <contents>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-f2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-14d" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x35c9</size>
         <contents>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-157" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe8</size>
         <contents>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-85"/>
         </contents>
      </logical_group>
      <logical_group id="lg-160" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-16a" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa10</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-16b" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x31</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-16c" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xa10</used_space>
         <unused_space>0x1f5f0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x918</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x9d8</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x9e8</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xa10</start_address>
               <size>0x1f5f0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x231</used_space>
         <unused_space>0x7dcf</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-123"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-125"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x31</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200031</start_address>
               <size>0x7dcf</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x9e8</load_address>
            <load_size>0x15</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x31</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xa08</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xa10</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xa10</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xa00</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xa08</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-43">
         <name>init_encoder</name>
         <value>0x955</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-44">
         <name>calc_speed</name>
         <value>0x93d</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-45">
         <name>encoder_cnt</name>
         <value>0x2020001c</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-46">
         <name>cur_speed</name>
         <value>0x20200018</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-47">
         <name>calc_encoder_cnt</name>
         <value>0x621</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-55">
         <name>main</name>
         <value>0x53d</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-56">
         <name>aim_speed</name>
         <value>0x20200014</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-57">
         <name>SysTick_Handler</name>
         <value>0x9af</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-58">
         <name>GROUP1_IRQHandler</name>
         <value>0x9a7</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-75">
         <name>SYSCFG_DL_init</name>
         <value>0x925</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-76">
         <name>SYSCFG_DL_initPower</name>
         <value>0x819</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-77">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x681</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-78">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x84d</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-79">
         <name>SYSCFG_DL_PWMA_init</name>
         <value>0x5b5</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x87d</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-85">
         <name>Default_Handler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>Reset_Handler</name>
         <value>0x9cb</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-87">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-88">
         <name>NMI_Handler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>HardFault_Handler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>SVC_Handler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>PendSV_Handler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>GROUP0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>TIMG8_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>UART3_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>ADC0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>ADC1_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>CANFD0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>DAC0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>SPI0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>SPI1_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>UART1_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>UART2_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>UART0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>TIMG0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>TIMG6_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>TIMA0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>TIMA1_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>TIMG7_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9d">
         <name>TIMG12_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>I2C0_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>I2C1_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a0">
         <name>AES_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a1">
         <name>RTC_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a2">
         <name>DMA_IRQHandler</name>
         <value>0x433</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-b6">
         <name>Pid_Speed</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-b7">
         <name>kp</name>
         <value>0x2020002c</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-b8">
         <name>ki</name>
         <value>0x20200028</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-b9">
         <name>kd</name>
         <value>0x20200024</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-c7">
         <name>init_moto</name>
         <value>0x98d</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-c8">
         <name>n20_moto</name>
         <value>0x6dd</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-c9">
         <name>g_dir</name>
         <value>0x20200030</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-ca">
         <name>g_pwm</name>
         <value>0x20200020</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-cb">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-cc">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-cd">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ce">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-cf">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d0">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d1">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d2">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d3">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-dc">
         <name>DL_Common_delayCycles</name>
         <value>0x99d</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-f3">
         <name>DL_Timer_setClockConfig</name>
         <value>0x8f1</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-f4">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x97d</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-f5">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x8d5</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-f6">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x90d</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-f7">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-102">
         <name>_c_int00_noargs</name>
         <value>0x8ad</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-103">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-10f">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x769</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-117">
         <name>_system_pre_init</name>
         <value>0x9cf</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-122">
         <name>__TI_decompress_none</name>
         <value>0x969</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-12d">
         <name>__TI_decompress_lzss</name>
         <value>0x4c1</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-137">
         <name>abort</name>
         <value>0x9c1</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-141">
         <name>HOSTexit</name>
         <value>0x9c7</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-142">
         <name>C$$EXIT</name>
         <value>0x9c6</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-148">
         <name>__aeabi_fadd</name>
         <value>0x2cb</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-149">
         <name>__addsf3</name>
         <value>0x2cb</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-14a">
         <name>__aeabi_fsub</name>
         <value>0x2c1</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-14b">
         <name>__subsf3</name>
         <value>0x2c1</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-151">
         <name>__aeabi_fmul</name>
         <value>0x435</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-152">
         <name>__mulsf3</name>
         <value>0x435</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-158">
         <name>__aeabi_f2iz</name>
         <value>0x7e1</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-159">
         <name>__fixsfsi</name>
         <value>0x7e1</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-15f">
         <name>__aeabi_i2f</name>
         <value>0x72d</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-160">
         <name>__floatsisf</name>
         <value>0x72d</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-166">
         <name>__aeabi_memcpy</name>
         <value>0x9b9</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-167">
         <name>__aeabi_memcpy4</name>
         <value>0x9b9</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-168">
         <name>__aeabi_memcpy8</name>
         <value>0x9b9</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-16e">
         <name>__muldsi3</name>
         <value>0x7a5</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-187">
         <name>memcpy</name>
         <value>0x399</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-188">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-18b">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-18c">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
